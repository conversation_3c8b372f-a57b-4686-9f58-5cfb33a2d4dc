<script setup lang="ts"></script>

<template>
  <UCard
    class="bg-neutral-50 dark:bg-neutral-800 hover:opacity-80 cursor-pointer relative"
  >
    <!-- Promotion Badge -->
    <div
      v-if="hasPromotion"
      class="absolute -top-2 -right-2 z-10"
    >
      <UBadge
        color="warning"
        variant="solid"
        size="sm"
        class="animate-pulse"
      >
        🎉 +100%
      </UBadge>
    </div>

    <div class="flex flex-col gap-2">
      <div
        class="text-2xl text-center items-center gap-2 justify-center flex flex-row"
      >
        <UIcon
          name="ic:twotone-generating-tokens"
          class="size-8 text-neutral-400 dark:text-neutral-500"
        />
        {{ title }}
      </div>
      <div class="text-sm text-center">
        {{ $t("non-expirable credits") }}
      </div>

      <!-- Show base + bonus if promotion is active -->
      <div
        v-if="hasPromotion && baseCredits"
        class="text-xs text-center text-amber-600 dark:text-amber-400"
      >
        {{ $t("Base: {base} + Bonus: {bonus}", {
          base: abbreviatedUnit(baseCredits),
          bonus: abbreviatedUnit(credits - baseCredits)
        }) }}
      </div>

      <div class="flex flex-col justify-center items-center gap-2">
        <UIcon
          name="i-lucide-arrow-down"
          class="size-5 text-neutral-400 dark:text-neutral-500"
        />
        <div class="text-xl">
          {{ price }}<small>$</small>
        </div>
      </div>
    </div>
  </UCard>
</template>

<script lang="ts" setup>
const props = defineProps<{
  credits: number
  baseCredits?: number
  price: number
  quantity: number
  hasPromotion?: boolean
}>()

const title = computed(() => {
  return `+ ${abbreviatedUnit(props.credits)}`
})
</script>
