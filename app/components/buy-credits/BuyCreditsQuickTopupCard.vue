<script setup lang="ts">
const props = defineProps<{
  credits: number
  baseCredits?: number
  price: number
  quantity: number
  hasPromotion?: boolean
}>()

const title = computed(() => {
  return `+ ${abbreviatedUnit(props.credits)}`
})
</script>

<template>
  <UCard
    class="bg-neutral-50 dark:bg-neutral-800 hover:opacity-80 cursor-pointer relative"
  >
    <!-- Promotion Badge -->
    <div
      v-if="hasPromotion"
      class="absolute -top-3 -right-3 z-10"
    >
      <div class="relative">
        <!-- Glow effect background -->
        <div class="absolute inset-0 bg-gradient-to-r from-amber-400 to-orange-500 rounded-full blur-sm opacity-75 animate-pulse" />

        <!-- Main badge -->
        <div class="relative bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 text-white px-2.5 py-1 rounded-full shadow-lg transform rotate-12 hover:rotate-0 transition-transform duration-300">
          <div class="flex items-center gap-1 text-xs font-bold">
            <span class="animate-bounce">🎉</span>
            <span class="bg-gradient-to-r from-white to-yellow-100 bg-clip-text text-transparent">+100%</span>
          </div>
        </div>

        <!-- Sparkle effects -->
        <div class="absolute -top-1 -left-1 w-2 h-2 bg-yellow-300 rounded-full animate-ping opacity-75" />
        <div
          class="absolute -bottom-1 -right-1 w-1.5 h-1.5 bg-orange-300 rounded-full animate-ping opacity-75"
          style="animation-delay: 0.5s;"
        />
        <div
          class="absolute top-0 right-0 w-1 h-1 bg-red-300 rounded-full animate-ping opacity-75"
          style="animation-delay: 1s;"
        />
      </div>
    </div>

    <div class="flex flex-col gap-2">
      <div
        class="text-2xl text-center items-center gap-2 justify-center flex flex-row"
      >
        <UIcon
          name="ic:twotone-generating-tokens"
          class="size-8 text-neutral-400 dark:text-neutral-500"
        />
        {{ title }}
      </div>
      <div class="text-sm text-center">
        {{ $t("non-expirable credits") }}
      </div>

      <!-- Show base + bonus if promotion is active -->
      <div
        v-if="hasPromotion && baseCredits"
        class="text-xs text-center text-amber-600 dark:text-amber-400"
      >
        {{ $t("Base: {base} + Bonus: {bonus}", {
          base: abbreviatedUnit(baseCredits),
          bonus: abbreviatedUnit(credits - baseCredits)
        }) }}
      </div>

      <div class="flex flex-col justify-center items-center gap-2">
        <UIcon
          name="i-lucide-arrow-down"
          class="size-5 text-neutral-400 dark:text-neutral-500"
        />
        <div class="text-xl">
          {{ price }}<small>$</small>
        </div>
      </div>
    </div>
  </UCard>
</template>
