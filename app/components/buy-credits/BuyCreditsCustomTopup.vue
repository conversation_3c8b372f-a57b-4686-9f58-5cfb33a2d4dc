<script setup lang="ts">
const creditsStore = useCreditsStore()
const productStore = useProductStore()
const { buyCreditProduct, creditUnitPrice } = storeToRefs(creditsStore)
const { authorize } = useAuthorize()
// Default to 1000 base credits (equivalent to $10 minimum)
const value = ref(1000)

const estimatedPrice = computed(() => {
  // New rate: 100 base credits = $1
  return (value.value / 100).toFixed(2)
})

// Calculate credits with promotion
const creditsWithPromotion = computed(() => {
  return creditsStore.getCreditsWithPromotion(value.value)
})

// Calculate bonus credits
const bonusCredits = computed(() => {
  if (productStore.isPromotionActive) {
    return creditsWithPromotion.value - value.value
  }
  return 0
})

// Check if purchase amount meets minimum requirement
const canPurchase = computed(() => {
  return parseFloat(estimatedPrice.value) >= 10
})

// Handle purchase with validation
const handlePurchase = () => {
  if (!canPurchase.value) {
    const toast = useToast()
    toast.add({
      id: 'minimum-purchase-error',
      title: 'Minimum Purchase Required',
      description: 'Minimum purchase amount is $10. Please increase your selection.',
      color: 'error'
    })
    return
  }

  authorize(() => creditsStore.processBuyCredits(creditsWithPromotion.value), 'any', 0)
}
</script>

<template>
  <div class="">
    <div>
      <h2 class="text-xl font-bold mb-4">
        {{ $t('customTopUp') }}
      </h2>
    </div>
    <div class="flex flex-col gap-4 justify-center items-center sm:max-w-lg mx-auto">
      <!-- Promotion Banner -->
      <div
        v-if="productStore.isPromotionActive"
        class="w-full bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3"
      >
        <div class="flex items-center gap-2 justify-center">
          <UIcon
            name="i-lucide-gift"
            class="size-5 text-amber-600 dark:text-amber-400"
          />
          <span class="text-sm font-medium text-amber-800 dark:text-amber-200">
            {{ $t("100% Bonus Credits Active!") }}
          </span>
        </div>
      </div>

      <UFormField
        :label="$t('numberOfCredits')"
        class="w-full"
      >
        <UInputNumber
          v-model="value"
          size="xl"
          class="w-full"
          :step="100"
          :min="1000"
        />
      </UFormField>

      <!-- Credits breakdown -->
      <div
        v-if="productStore.isPromotionActive && bonusCredits > 0"
        class="w-full text-center text-sm text-amber-600 dark:text-amber-400"
      >
        {{ $t("You'll receive: {total} credits ({base} base + {bonus} bonus)", {
          total: formatNumber(creditsWithPromotion),
          base: formatNumber(value),
          bonus: formatNumber(bonusCredits)
        }) }}
      </div>

      <UButton
        size="xl"
        color="primary"
        block
        :disabled="!canPurchase"
        @click="handlePurchase"
      >
        ${{ estimatedPrice }} - {{ $t("Top up now") }}
      </UButton>

      <!-- Minimum Purchase Warning -->
      <div
        v-if="!canPurchase"
        class="text-center text-sm text-red-600 dark:text-red-400"
      >
        {{ $t("Minimum purchase amount is $10") }}
      </div>
    </div>
  </div>
</template>
