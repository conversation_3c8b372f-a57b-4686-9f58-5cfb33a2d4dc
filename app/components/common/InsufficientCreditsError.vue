<script setup lang="ts">
import { formatNumber } from '~/utils'

interface Props {
  // Required props
  creditsNeeded: number
  availableCredits?: number

  // Optional props for customization
  title?: string
  message?: string
  generationType?: string // e.g., 'video', 'speech', 'image', 'dialog'

  // Custom action handlers
  onBuyCredits?: () => void
  onViewPricing?: () => void

  // Show/hide elements
  showPurchaseAmount?: boolean
  showMinimumNotice?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  availableCredits: 0,
  title: '',
  message: '',
  generationType: 'generation',
  showPurchaseAmount: true,
  showMinimumNotice: true
})

const emit = defineEmits<{
  buyCredits: []
  viewPricing: []
}>()

const { t } = useI18n()
const router = useRouter()
const authStore = useAuthStore()
const creditsStore = useCreditsStore()
const productStore = useProductStore()

// Get user credit from auth store
const actualAvailableCredits = computed(() => props.availableCredits || authStore.$state.user?.user_credit?.available_credit || 0)

// Calculate shortfall
const creditsShortfall = computed(() => {
  return Math.max(0, props.creditsNeeded - actualAvailableCredits.value)
})

// Calculate actual purchase amount (minimum $10 worth)
const actualPurchaseAmount = computed(() => {
  const creditsStore = useCreditsStore()
  const productStore = useProductStore()

  // Minimum $10 = 1000 base credits, with promotion = 2000 total credits
  const baseCreditsFor10USD = 1000 // $10 = 1000 base credits
  const creditsFor10USDWithPromotion = creditsStore.getCreditsWithPromotion(baseCreditsFor10USD)

  return Math.max(creditsShortfall.value, creditsFor10USDWithPromotion)
})

// Dynamic title and message based on generation type
const displayTitle = computed(() => {
  return props.title || t('NOT_ENOUGH_CREDIT_TITLE')
})

const displayMessage = computed(() => {
  if (props.message) return props.message

  // Generate dynamic message based on generation type
  const typeKey = `NOT_ENOUGH_CREDIT_MESSAGE_${props.generationType.toUpperCase()}`
  const fallbackMessage = t('NOT_ENOUGH_CREDIT_MESSAGE')

  // Try specific message first, fallback to generic
  return t(typeKey, {}, fallbackMessage)
})

// Handle buy credits
const handleBuyCredits = () => {
  if (props.onBuyCredits) {
    props.onBuyCredits()
  } else {
    // Default behavior - open credits drawer with calculated amount
    creditsStore.processBuyCredits(actualPurchaseAmount.value)
  }
  emit('buyCredits')
}

// Handle view pricing
const handleViewPricing = () => {
  if (props.onViewPricing) {
    props.onViewPricing()
  } else {
    // Default behavior - navigate to pricing page
    router.push('/pricing')
  }
  emit('viewPricing')
}

// Dynamic label keys based on generation type
const creditsNeededKey = computed(() => {
  const typeKey = `${props.generationType}Gen.creditsNeeded`
  return t(typeKey, {}, t('creditsNeeded'))
})

const creditsAvailableKey = computed(() => {
  const typeKey = `${props.generationType}Gen.creditsAvailable`
  return t(typeKey, {}, t('creditsAvailable'))
})

const creditsShortfallKey = computed(() => {
  const typeKey = `${props.generationType}Gen.creditsShortfall`
  return t(typeKey, {}, t('creditsShortfall'))
})

const willPurchaseKey = computed(() => {
  const typeKey = `${props.generationType}Gen.willPurchase`
  return t(typeKey, {}, t('willPurchase'))
})
</script>

<template>
  <div class="text-center space-y-6 max-w-md mx-auto">
    <!-- Credit icon with gradient background -->
    <div class="relative">
      <div class="w-20 h-20 mx-auto bg-gradient-to-r from-amber-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
        <UIcon
          name="i-lucide-coins"
          class="text-4xl text-white"
        />
      </div>
    </div>

    <!-- Error title and message -->
    <div class="space-y-3">
      <h3 class="text-xl font-bold text-gray-900 dark:text-white">
        {{ displayTitle }}
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-300 leading-relaxed whitespace-break-spaces">
        {{ displayMessage }}
      </p>

      <!-- Promotion Banner -->
      <div
        v-if="productStore.isPromotionActive"
        class="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3"
      >
        <div class="flex items-center gap-2 justify-center">
          <UIcon
            name="i-lucide-gift"
            class="size-5 text-amber-600 dark:text-amber-400"
          />
          <span class="text-sm font-medium text-amber-800 dark:text-amber-200">
            {{ $t("🎉 100% Bonus Credits Active! Get double credits with every purchase!") }}
          </span>
        </div>
      </div>

      <!-- Credit info -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-2">
        <div class="flex justify-between items-center text-sm">
          <span class="text-gray-600 dark:text-gray-400">{{ creditsNeededKey }}:</span>
          <span class="font-medium text-gray-900 dark:text-white">
            {{ formatNumber(creditsNeeded) }}
          </span>
        </div>
        <div class="flex justify-between items-center text-sm">
          <span class="text-gray-600 dark:text-gray-400">{{ creditsAvailableKey }}:</span>
          <span class="font-medium text-gray-900 dark:text-white">
            {{ formatNumber(actualAvailableCredits) }}
          </span>
        </div>
        <UDivider />
        <div class="flex justify-between items-center text-sm font-semibold">
          <span class="text-red-600 dark:text-red-400">{{ creditsShortfallKey }}:</span>
          <span class="text-red-600 dark:text-red-400">
            {{ formatNumber(creditsShortfall) }}
          </span>
        </div>

        <!-- Show actual purchase amount if different from shortfall -->
        <div
          v-if="showPurchaseAmount && actualPurchaseAmount > creditsShortfall"
          class="flex justify-between items-center text-sm font-semibold pt-2 border-t border-gray-200 dark:border-gray-600"
        >
          <span class="text-primary-600 dark:text-primary-400">{{ willPurchaseKey }}:</span>
          <span class="text-primary-600 dark:text-primary-400">
            {{ formatNumber(actualPurchaseAmount) }} {{ $t("credits") }}
          </span>
        </div>
      </div>
    </div>

    <!-- Action buttons -->
    <div class="space-y-3">
      <div class="flex flex-row gap-2">
        <UButton
          color="primary"
          size="lg"
          :label="$t('BUY_CREDITS_NOW')"
          icon="i-lucide-credit-card"
          class="w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700"
          @click="handleBuyCredits"
        />
        <UButton
          color="neutral"
          variant="outline"
          size="md"
          :label="$t('VIEW_PRICING')"
          icon="i-lucide-external-link"
          class="w-full"
          @click="handleViewPricing"
        />
      </div>

      <!-- Minimum purchase notice -->
      <div
        v-if="showMinimumNotice"
        class="text-center"
      >
        <span class="text-xs text-gray-500 dark:text-gray-400">
          {{ $t('MINIMUM_PURCHASE_10') }}
        </span>
      </div>
    </div>
  </div>
</template>
