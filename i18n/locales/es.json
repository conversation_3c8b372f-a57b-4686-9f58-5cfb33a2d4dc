{"system": "Generador de Imágenes IA", "helloWorld": "¡Hola Mundo!", "Describe the image you want to generate...": "Describe la imagen que quieres generar...", "appTitle": "Creación de Imágenes IA", "copyright": "Copyright © {year}, Creación de Imágenes IA", "available": "Disponible para nuevos proyectos", "notAvailable": "No disponible en este momento", "blog": "Blog", "copyLink": "<PERSON><PERSON><PERSON> enlace", "minRead": "MIN LECTURA", "articleLinkCopied": "Enlace del artículo copiado al portapapeles", "clickToClose": "Haz clic en cualquier lugar o presiona ESC para cerrar", "promptDetails": "Detalles del prompt", "generateWithPrompt": "Generar con este prompt", "generateWithSettings": "Generar con estas configuraciones", "preset": "<PERSON><PERSON><PERSON><PERSON>", "style": "<PERSON><PERSON><PERSON>", "resolution": "Resolución", "addImage": "<PERSON><PERSON><PERSON>", "modelPreset": "Modelo/Preajuste", "imageDimensions": "Dimensiones de Imagen", "yourImage": "<PERSON>", "generate": "Generar", "nav.aitool": "Herramienta IA", "nav.api": "API", "nav.login": "<PERSON><PERSON><PERSON>", "nav.history": "Historia", "nav.orders": "<PERSON><PERSON><PERSON>", "3D Render": "Renderizado 3D", "Acrylic": "Acrílico", "Anime General": "Anime General", "Creative": "Creativo", "Dynamic": "Dinámico", "Fashion": "Moda", "Game Concept": "<PERSON><PERSON>", "Graphic Design 3D": "Diseño Gráfico 3D", "Illustration": "Ilustración", "None": "<PERSON><PERSON><PERSON>", "Portrait": "Retrato", "Portrait Cinematic": "Retrato Cinematográfico", "Portrait Fashion": "Retrato de Moda", "Ray Traced": "Trazado de Rayos", "Stock Photo": "Foto de Stock", "Watercolor": "<PERSON><PERSON><PERSON><PERSON>", "AI Image Generator": "Generador de Imágenes IA", "Generate AI images from text prompts with a magical particle transformation effect": "Genera imágenes IA a partir de prompts de texto con un efecto mágico de transformación de partículas", "Enter your prompt": "Ingresa tu prompt", "Generating...": "Generando...", "Generate Image": "<PERSON><PERSON>n", "Enter a prompt and click Generate Image to create an AI image": "Ingresa un prompt y haz clic en Generar Imagen para crear una imagen IA", "Prompt:": "Prompt:", "Download": "<PERSON><PERSON><PERSON>", "How It Works": "Cómo Funciona", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "Este generador de imágenes IA usa un efecto de transformación basado en partículas para visualizar el proceso de creación. Cuando ingresas un prompt y haces clic en 'Generar', el sistema:", "Sends your prompt to an AI image generation API": "Envía tu prompt a una API de generación de imágenes IA", "Creates a particle system with thousands of tiny particles": "Crea un sistema de partículas con miles de pequeñas partículas", "Transforms the random noise particles into the generated image": "Transforma las partículas de ruido aleatorio en la imagen generada", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "Las partículas comienzan en un patrón de ruido aleatorio y luego se transforman suavemente en la imagen final, creando un efecto mágico que simula el proceso creativo de la IA.", "Transform": "Transformar", "Transforming...": "Transformando...", "Initializing particles...": "Inicializando partículas...", "Loading image...": "Cargando imagen...", "Creating particle system...": "Creando sistema de partículas...", "Adding event listeners...": "Añadiendo detectores de eventos...", "Ready!": "¡Listo!", "AI Image Particle Effect": "Efecto de Partículas de Imagen IA", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "Una demostración del componente BaseMagicImage que transforma partículas en imágenes generadas por IA", "Click anywhere or press ESC to close": "Haz clic en cualquier lugar o presiona ESC para cerrar", "auth.login": "<PERSON><PERSON><PERSON>", "auth.loginDescription": "Inicia sesión en tu cuenta para continuar", "auth.email": "Correo Electrónico", "auth.enterEmail": "Ingresa tu correo electrónico", "auth.password": "Contraseña", "auth.enterPassword": "Ingresa tu contraseña", "auth.rememberMe": "Recordarme", "auth.welcomeBack": "Bienvenido de vuelta", "auth.signupFailed": "Registro fallido", "auth.signupFailedDescription": "Hubo un error durante el registro. Por favor, inténtalo de nuevo.", "auth.dontHaveAccount": "¿No tienes una cuenta?", "auth.signUp": "Registrarse", "auth.forgotPassword": "¿Olvidaste tu contraseña?", "auth.bySigningIn": "Al iniciar sesi<PERSON>, aceptas nuestros", "auth.termsOfService": "Términos de Servicio", "auth.signUpTitle": "Registrarse", "auth.signUpDescription": "<PERSON>rea una cuenta para comenzar", "auth.name": "Nombre", "auth.enterName": "Ingresa tu nombre", "auth.createAccount": "<PERSON><PERSON><PERSON> cuenta", "auth.alreadyHaveAccount": "¿Ya tienes una cuenta?", "auth.bySigningUp": "Al registrarte, aceptas nuestros", "auth.backToHome": "Volver al inicio", "auth.notVerifyAccount": "Tu cuenta no está verificada. Por favor, verifica tu cuenta para continuar", "auth.verifyAccount": "Verificar cuenta", "auth.resendActivationEmail": "Reenviar correo de activación", "auth.accountRecovery": "Recuperación de Cuenta", "auth.accountRecoveryTitle": "Recupera tu cuenta", "auth.accountRecoveryDescription": "Ingresa tu correo electrónico para recibir instrucciones de restablecimiento de contraseña", "auth.sendRecoveryEmail": "Enviar correo de recuperación", "auth.recoveryEmailSent": "Correo de recuperación enviado", "auth.recoveryEmailSentDescription": "Por favor, revisa tu correo electrónico para instrucciones de restablecimiento de contraseña", "auth.resetPassword": "Restable<PERSON>", "auth.resetPasswordTitle": "Restablece tu contraseña", "auth.resetPasswordDescription": "Ingresa tu nueva contraseña", "auth.newPassword": "Nueva contraseña", "auth.confirmPassword": "Confirmar con<PERSON>", "auth.enterNewPassword": "Ingresa tu nueva contraseña", "auth.enterConfirmPassword": "Confirma tu nueva contraseña", "auth.passwordResetSuccess": "Restablecimiento de contraseña exitoso", "auth.passwordResetSuccessDescription": "Tu contraseña ha sido restablecida exitosamente. Ahora puedes iniciar sesión con tu nueva contraseña", "auth.activateAccount": "Activar Cuenta", "auth.activateAccountTitle": "Activa tu cuenta", "auth.activateAccountDescription": "Tu cuenta está siendo activada...", "auth.accountActivated": "Cuenta activada", "auth.accountActivatedDescription": "Tu cuenta ha sido activada exitosamente. Ahora puedes iniciar sesión", "auth.activationFailed": "Activación fallida", "auth.activationFailedDescription": "Error al activar tu cuenta. Por favor, inténtalo de nuevo o contacta soporte", "auth.backToLogin": "Volver al inicio de sesión", "auth.loginFailed": "Error al iniciar sesión", "auth.loginWithGoogle": "Iniciar se<PERSON><PERSON> con Google", "auth.google": "Google", "auth.filter": "Filter", "validation.invalidEmail": "Correo electrónico inválido", "validation.passwordMinLength": "La contraseña debe tener al menos 8 caracteres", "validation.nameRequired": "El nombre es requerido", "validation.required": "Este campo es requerido", "validation.passwordsDoNotMatch": "Las contraseñas no coinciden", "validation.usernameMinLength": "El nombre de usuario debe tener al menos 2 caracteres", "validation.usernameMaxLength": "El nombre de usuario no puede tener más de 50 caracteres", "validation.usernameInvalidCharacters": "El nombre de usuario solo puede contener letras, números, guiones bajos y guiones", "imageSelect.pleaseSelectImageFile": "Por favor, selecciona un archivo de imagen", "imageSelect.selectedImage": "Imagen se<PERSON>cci<PERSON>", "imageSelect.removeImage": "Eliminar imagen", "pixelReveal.loading": "Cargando imagen...", "pixelReveal.processing": "Procesando imagen...", "pixelReveal.revealComplete": "Revelación de imagen completa", "SIGNIN_WRONG_EMAIL_PASSWORD": "Correo electrónico o contraseña incorrectos", "Try again": "Intentar de nuevo", "aiToolMenu.imagen": "Imagen", "aiToolMenu.videoGen": "Video Gen", "aiToolMenu.speechGen": "Speech Gen", "aiToolMenu.musicGen": "Music Gen", "aiToolMenu.imagen3": "Imagen 3", "aiToolMenu.imagen3Description": "Genera imágenes de alta calidad y detalladas con renderizado de texto preciso para contenido visual creativo.", "aiToolMenu.imagen4": "Imagen 4", "aiToolMenu.imagen4Description": "Expresa tus ideas como nunca antes — con Imagen, la creatividad no tiene límites.", "aiToolMenu.gemini2Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini2FlashDescription": "Gemini 2.5 Flash es una herramienta poderosa para generar imágenes a partir de prompts de texto.", "aiToolMenu.veo2": "Veo 2", "aiToolMenu.veo2Description": "Mayor control, consistencia y creatividad que nunca antes.", "aiToolMenu.veo3": "Veo 3", "aiToolMenu.veo3Description": "Video, conoce audio. Nuestro último modelo de generación de video, diseñado para empoderar a cineastas y narradores.", "aiToolMenu.gemini25Pro": "Gemini 2.5 Pro", "aiToolMenu.gemini25ProDescription": "El modelo de texto a voz más avanzado disponible.", "aiToolMenu.gemini25Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini25FlashDescription": "Procesamiento a gran escala (ej. múltiples pdfs).\nTareas de bajo tiempo de latencia y alto volumen que requieren pensamiento\nCasos de uso agénticos", "aiToolMenu.link": "Enlace", "aiToolMenu.linkDescription": "Usa NuxtLink con superpoderes.", "aiToolMenu.soon": "Pronto", "readArticle": "<PERSON><PERSON>", "switchToLightMode": "Cambiar a modo claro", "switchToDarkMode": "Cambiar a modo oscuro", "profile": "Perfil", "buyCredits.checkout": "Finalizar Compra", "buyCredits.checkoutDescription": "Confirma tu pedido y luego elige tu método de pago.", "buyCredits.orderDetail": "Detalle del pedido", "buyCredits.credits": "C<PERSON>dit<PERSON>", "buyCredits.pricePerUnit": "Precio por unidad", "buyCredits.totalCredits": "Total de créditos", "buyCredits.totalPrice": "Precio total", "buyCredits.payment": "Pago", "buyCredits.submit": "Enviar", "buyCredits.cancel": "<PERSON><PERSON><PERSON>", "pricing.title": "<PERSON><PERSON><PERSON>", "pricing.description": "Elige el plan perfecto para tus necesidades de generación de imágenes", "pricing.comingSoon": "Próximamente", "pricing.comingSoonDescription": "Nuestros planes de precios están siendo finalizados. Vuelve pronto para actualizaciones.", "magicImageDemo.title": "Efecto de Partículas de Imagen IA", "magicImageDemo.description": "Una demostración del componente BaseMagicImage que transforma partículas en imágenes generadas por IA", "magicImageDemo.image": "Imagen", "magicImageDemo.aboutTitle": "Acerca de Este Componente", "magicImageDemo.aboutDescription": "El componente BaseMagicImage usa Three.js para crear un sistema de partículas que puede transformarse entre posiciones aleatorias y una imagen generada por IA. Las partículas se mueven con efectos de remolino y flujo, creando una transformación mágica.", "magicImageDemo.featuresTitle": "Características", "magicImageDemo.features.particleRendering": "Renderizado de imagen basado en partículas", "magicImageDemo.features.smoothTransitions": "Transiciones suaves entre posiciones aleatorias de partículas y formación de imagen", "magicImageDemo.features.interactiveControls": "Controles de cámara interactivos (arrastra para rotar, desplaza para hacer zoom)", "magicImageDemo.features.customizable": "Cantidad de partículas y duración de animación personalizables", "magicImageDemo.features.automatic": "Activación de transformación automática o manual", "magicImageDemo.howItWorksTitle": "Cómo Funciona", "magicImageDemo.howItWorksDescription": "El componente analiza los píxeles de una imagen y crea un sistema de partículas 3D donde cada partícula representa un píxel. Los píxeles más brillantes se posicionan más cerca del espectador, creando un efecto 3D sutil. Las partículas inicialmente se dispersan aleatoriamente en el espacio 3D, luego se animan para formar la imagen cuando se activa.", "privacy.title": "Política de Privacidad", "privacy.description": "Aprende cómo protegemos tu privacidad y manejamos tus datos", "privacy.informationWeCollect": "Información que Recopilamos", "privacy.dataSecurity": "Seguridad de Datos", "privacy.dataSecurityDescription": "Implementamos medidas de seguridad apropiadas para proteger tu información personal contra acceso no autorizado, alteración, divulgación o destrucción.", "privacy.contactUs": "Contáctanos", "terms.title": "Términos de Servicio", "terms.description": "Términos y condiciones para usar los servicios de Imagen", "terms.acceptanceOfTerms": "1. Aceptación de Términos", "terms.acceptanceOfTermsDescription": "Al acceder y usar los servicios de Imagen, aceptas y acuerdas estar sujeto a los términos y disposiciones de este acuerdo.", "terms.useOfService": "2. <PERSON><PERSON> Servicio", "terms.userAccounts": "3. Cuentas de Usuario", "terms.userAccountsDescription": "Eres responsable de mantener la confidencialidad de tu cuenta y contraseña.", "terms.intellectualProperty": "4. Propiedad Intelectual", "terms.intellectualPropertyDescription": "Todo el contenido y materiales disponibles en nuestro servicio están protegidos por derechos de propiedad intelectual.", "terms.termination": "5. Terminación", "terms.terminationDescription": "Podemos terminar o suspender tu cuenta y acceso al servicio a nuestra sola discreción.", "terms.disclaimers": "6. <PERSON><PERSON><PERSON> de Responsabilidad", "terms.disclaimersDescription": "El servicio se proporciona 'como está' sin garantías de ningún tipo.", "terms.contactUsTerms": "Contáctanos", "terms.contactUsTermsDescription": "Si tienes alguna pregunta sobre estos Términos de Servicio, por favor contáctanos a través de nuestros canales de soporte.", "Describe the video you want to generate...": "Describe el video que deseas generar...", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "appName": "GeminiGen.AI", "quickTopUp": "<PERSON><PERSON><PERSON>", "customTopUp": "Recarga personalizada", "numberOfCredits": "Número de créditos", "paypal": "PayPal", "paypalDescription": "Pague de manera segura con su cuenta de PayPal", "stripe": "Stripe", "stripeDescription": "Pague de manera segura con Stripe", "debitCreditCard": "Tarjeta de débito o crédito", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "Paga con Cripto", "cryptoDescription": "Bitcoin, Ethereum y otras criptomonedas", "profileMenu.guide": "Guía", "profileMenu.logo": "Logotipo", "profileMenu.settings": "Configuración", "profileMenu.components": "Componentes", "loadingMoreItems": "Cargando más elementos...", "promptLabel": "Pronta:", "videoExamples": "Ejemplos de video", "videoExamplesDescription": "Explora estos ejemplos de video con sus indicaciones y configuraciones. Haz clic en cualquier botón de 'Usar este mensaje' para copiar el mensaje a tu campo de entrada.", "useThisPrompt": "Usa este aviso", "model": "<PERSON><PERSON>", "duration": "Duración", "videoTypeSelection": "Seleccionar tipo de video", "notifications.title": "Notifications", "notifications.description": "Your recent notifications and updates", "notifications.totalCount": "{count} notifications", "notifications.markAllRead": "Mark all as read", "notifications.loadMore": "Load more", "notifications.close": "Close", "notifications.empty.title": "No notifications", "notifications.empty.description": "You're all caught up! No new notifications to show.", "notifications.error.title": "Error loading notifications", "notifications.types.default.title": "Notification", "notifications.types.default.description": "You have a new notification", "notifications.types.video_1.title": "Generación de video pendiente", "notifications.types.video_1.description": "La generación de video está esperando ser procesada.", "notifications.types.video_2.title": "Generación de video completada", "notifications.types.video_2.description": "El video se ha generado con éxito.", "notifications.types.video_3.title": "Falló la generación de video", "notifications.types.video_3.description": "La generación de video falló", "notifications.types.image_1.title": "Generación de imagen pendiente", "notifications.types.image_1.description": "La generación de imágenes está esperando ser procesada.", "notifications.types.image_2.title": "Generación de imagen completada", "notifications.types.image_2.description": "La imagen se ha generado con éxito.", "notifications.types.image_3.title": "Fallo en la Generación de Imágenes", "notifications.types.image_3.description": "La generación de imágenes falló", "notifications.types.tts_history_1.title": "Generación de audio pendiente", "notifications.types.tts_history_1.description": "El texto a voz está esperando ser procesado.", "notifications.types.tts_history_2.title": "Generación de audio completada", "notifications.types.tts_history_2.description": "El audio de texto a voz se ha generado con éxito.", "notifications.types.tts_history_3.title": "Error de generación de audio", "notifications.types.tts_history_3.description": "La generación de texto a voz falló", "notifications.types.voice_training_1.title": "Entrenamiento de Voz Pendiente", "notifications.types.voice_training_1.description": "El entrenamiento de voz está esperando para ser procesado.", "notifications.types.voice_training_2.title": "Entrenamiento de Voz Completado", "notifications.types.voice_training_2.description": "El entrenamiento del modelo de voz personalizado se ha completado con éxito.", "notifications.types.voice_training_3.title": "Entrenamiento de voz fallido", "notifications.types.voice_training_3.description": "El entrenamiento de voz falló", "notifications.types.music_1.title": "Generación de Música Pendiente", "notifications.types.music_1.description": "La generación de música está esperando ser procesada.", "notifications.types.music_2.title": "Generación de Música Completa", "notifications.types.music_2.description": "La música de IA se ha generado con éxito.", "notifications.types.music_3.title": "Generación de música fallida", "notifications.types.music_3.description": "La generación de música falló", "notifications.types.speech_1.title": "Generación de discurso pendiente", "notifications.types.speech_1.description": "Su solicitud de generación de discursos está esperando ser procesada.", "notifications.types.speech_2.title": "Generación de discurso completa.", "notifications.types.speech_2.description": "Su discurso ha sido generado exitosamente.", "notifications.types.speech_3.title": "Falló la Generación de Voz", "notifications.types.speech_3.description": "La generación de discurso falló. Por favor, inténtalo de nuevo.", "notifications.time.justNow": "<PERSON><PERSON><PERSON>", "notifications.time.minutesAgo": "Hace {minutes} minutos", "notifications.time.hoursAgo": "Hace {hours} horas", "notifications.time.yesterday": "Ayer", "notifications.status.processing.title": "Procesamiento", "notifications.status.processing.description": "Su solicitud está siendo procesada.", "notifications.status.success.title": "Completado", "notifications.status.success.description": "Completado con éxito", "notifications.status.failed.title": "Fallido", "notifications.status.failed.description": "Ocurrió un error durante el procesamiento.", "notifications.status.warning.title": "Advertencia", "notifications.status.warning.description": "Completado con advertencias", "notifications.status.pending.title": "Pendiente", "notifications.status.pending.description": "<PERSON><PERSON>ando ser procesado", "notifications.status.cancelled.title": "Cancelado", "notifications.status.cancelled.description": "La solicitud fue cancelada.", "footer.nuxtUIOnDiscord": "Nuxt UI on Discord", "profileSettings.emailNotifications": "Email Notifications", "profileSettings.marketingEmails": "Marketing Emails", "profileSettings.securityAlerts": "Security Alerts", "settings": "Configuración", "userMenu.profile": "Perfil", "userMenu.buyCredits": "<PERSON>mp<PERSON>", "userMenu.settings": "Configuración", "userMenu.api": "API", "userMenu.logout": "<PERSON><PERSON><PERSON>", "userMenu.greeting": "<PERSON><PERSON>, {name}", "formats.mp3": "MP3", "formats.wav": "WAV", "channels.mono": "Mono", "channels.stereo": "Estéreo", "options.allow": "<PERSON><PERSON><PERSON>", "options.dontAllow": "No permitir", "options.voices": "Voces", "options.pickVoice": "Elegir voz", "voiceTypes.systemVoices": "Voces del sistema", "voiceTypes.customVoices": "Voces personalizadas", "voiceTypes.premiumVoices": "Voces premium", "voiceTypes.userVoices": "Voces de usuario", "common.home": "<PERSON><PERSON>", "Describe the speech you want to generate...": "Describe el discurso que deseas generar...", "listenToSpeech": "Escuchar discurso", "generateSimilar": "Generar similar", "voice": "Voz", "emotion": "Emoción", "speed": "Velocidad", "speed_settings": "Ajustes de velocidad", "speed_value": "Valor de velocidad", "speed_slider": "Deslizador de velocidad", "apply": "Aplicar", "speech_settings": "Configuración de Voz", "current_speed": "Velocidad actual", "reset_defaults": "Restablecer a valores predeterminados", "outputFormat": "Formato de salida", "outputChannel": "Canal de salida", "selectVoice": "Seleccionar voz", "selectEmotion": "Seleccionar emoción", "selectFormat": "Seleccionar formato", "selectChannel": "Seleccionar canal", "noVoicesAvailable": "No hay voces disponibles", "noEmotionsAvailable": "No hay emociones disponibles", "searchVoices": "Buscar voces...", "searchEmotions": "Buscar emociones...", "noVoicesFound": "No se encontraron voces", "noEmotionsFound": "No se encontraron emociones", "retry": "Reintentar", "noAudioSample": "No hay muestra de audio disponible", "Speech Generation Complete": "Generación de discurso completa", "Your speech has been generated successfully": "Tu discurso ha sido generado con éxito.", "history.tabs.imagen": "Imagen", "history.tabs.video": "Video", "history.tabs.speech": "Discurso", "history.tabs.music": "Música", "history.tabs.history": "Historia", "orders.title": "Historial de pedidos", "orders.description": "Ver su historial de transacciones y pagos", "orders.orderId": "ID de pedido", "orders.amount": "Cantidad", "orders.credits": "C<PERSON>dit<PERSON>", "orders.quantity": "Cantidad", "orders.platform": "Plataforma", "orders.externalId": "ID de transacción", "orders.status.completed": "Completado", "orders.status.success": "Éxito", "orders.status.paid": "<PERSON><PERSON>", "orders.status.pending": "Pendiente", "orders.status.processing": "Procesamiento", "orders.status.failed": "Fallido", "orders.status.cancelled": "Cancelado", "orders.status.error": "Error", "orders.empty.title": "Aún no hay pedidos", "orders.empty.description": "Aún no has hecho ningún pedido. Compra créditos para comenzar a usar nuestros servicios.", "orders.empty.action": "<PERSON>mp<PERSON>", "orders.endOfList": "Has visto todos los pedidos.", "orders.errors.fetchFailed": "No se pudo cargar el historial de pedidos. Por favor, inténtalo de nuevo.", "orders.meta.title": "Historial de pedidos - Imagen AI", "orders.meta.description": "Consulta tu historial de transacciones y pagos en Imagen AI.", "historyPages.imagenDescription": "Navega por tus imágenes y obras de arte generadas por IA.", "historyPages.musicDescription": "Explora tu música y contenido de audio generados por IA.", "historyPages.speechDescription": "Examina tu contenido de voz y discurso generado por IA.", "historyPages.videoDescription": "Explora tus videos y animaciones generados por IA", "historyPages.imagenBreadcrumb": "Imagen", "historyPages.musicBreadcrumb": "Música", "historyPages.speechBreadcrumb": "Discurso", "historyPages.videoBreadcrumb": "Generación de videos", "historyPages.endOfImagesHistory": "Has llegado al final del historial de imágenes.", "historyPages.endOfMusicHistory": "Has llegado al final de la historia de la música.", "historyPages.endOfSpeechHistory": "Has llegado al final del historial de discursos.", "historyPages.endOfVideoHistory": "Has llegado al final del historial de videos.", "historyPages.noVideosFound": "No se encontraron videos", "historyPages.noVideosFoundDescription": "Comienza a generar videos para verlos aquí.", "historyPages.backToLibrary": "Volver a la biblioteca", "historyPages.errorLoadingVideo": "Error al cargar el video", "historyPages.loadingVideoDetails": "Cargando detalles del video...", "historyPages.videoDetails": "Detalles del video", "historyPages.videoInformation": "Información del video", "historyPages.videoNotFound": "El video que buscas no se pudo encontrar o cargar.", "historyPages.aiContentLibraryTitle": "Biblioteca de Contenidos de IA", "historyPages.aiContentLibraryDescription": "Navega y gestiona tu contenido generado por IA en distintas categorías.", "demo.notifications.title": "Tipos y estado de notificaciones de demostración", "demo.notifications.description": "Ejemplos de diferentes tipos de notificaciones con varios estados de estado", "demo.notifications.statusLegend": "Leyenda de estados", "demo.speechVoiceSelect.title": "Selección de Voz de Habla Demostración", "demo.speechVoiceSelect.description": "Demostración del componente reutilizable BaseSpeechVoiceSelectModal con las propiedades modelValue", "aspectRatio": "Relación de aspecto", "Image Reference": "Referencia de imagen", "personGeneration.dontAllow": "Don't Allow", "personGeneration.allowAdult": "Allow Adult", "personGeneration.allowAll": "Allow All", "safety_filter_level": "<PERSON>vel de Filtro de Seguridad", "used_credit": "Crédito utiliza<PERSON>", "Safety Filter": "Filtro de seguridad", "safetyFilter.blockLowAndAbove": "Bloque Bajo y Alto", "safetyFilter.blockMediumAndAbove": "Bloque medio y superior", "safetyFilter.blockOnlyHigh": "<PERSON><PERSON><PERSON><PERSON> solo alto", "safetyFilter.blockNone": "Bloquear Ninguno", "historyFilter.all": "Todo", "historyFilter.imagen": "Imagen", "historyFilter.videoGen": "Video Gen", "historyFilter.speechGen": "Generación de Speech", "Person Generation": "Generación de Personas", "downloadImage": "<PERSON><PERSON><PERSON> imagen", "noImageAvailable": "Imagen no disponible", "enhancePrompt": "Mejorar indicación", "addImages": "Agregar <PERSON>", "generateVideo": "Generar video", "happy": "<PERSON><PERSON><PERSON>", "sad": "Triste", "angry": "<PERSON><PERSON><PERSON>", "excited": "Emocionado", "laughing": "Riendo", "crying": "Llorando", "calm": "Calma", "serious": "Ser<PERSON>", "frustrated": "<PERSON><PERSON><PERSON>", "hopeful": "<PERSON><PERSON><PERSON>do", "narrative": "<PERSON><PERSON><PERSON><PERSON>", "kids' storytelling": "Narración Infantil", "audiobook": "Audiolibro", "poetic": "Poético", "mysterious": "Misterioso", "inspirational": "Inspirador", "surprised": "Sorpresa", "confident": "<PERSON><PERSON><PERSON>", "romantic": "R<PERSON><PERSON><PERSON><PERSON>", "scared": "<PERSON><PERSON><PERSON>", "trailer voice": "<PERSON><PERSON>", "advertising": "Publicidad", "documentary": "Documental", "newsreader": "Presentador de noticias", "weather report": "Informe del tiempo", "game commentary": "Comentario del juego", "interactive": "Interactivo", "customer support": "Atención al Cliente", "playful": "Juguetón", "tired": "Cansado", "sarcastic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disgusted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "whispering": "<PERSON><PERSON><PERSON><PERSON>", "persuasive": "<PERSON><PERSON><PERSON><PERSON>", "nostalgic": "Nostálgico", "meditative": "Meditativo", "announcement": "<PERSON><PERSON><PERSON>", "professional pitch": "Presentación profesional", "casual": "Informal", "exciting trailer": "<PERSON><PERSON><PERSON><PERSON> emocionante", "dramatic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "corporate": "Corporativo", "tech enthusiast": "Entusiasta de la tecnología", "youthful": "Juvenil", "calming reassurance": "Tranquilizante Reafirmación", "heroic": "Heroico", "festive": "Festivo", "urgent": "Urgente", "motivational": "Motivacional", "friendly": "<PERSON><PERSON><PERSON>", "energetic": "Energético", "serene": "<PERSON><PERSON>", "bold": "Negrita", "charming": "Encan<PERSON><PERSON>", "monotone": "Monótono", "questioning": "Cuestionamiento", "directive": "Directiva", "dreamy": "<PERSON><PERSON><PERSON>", "epic": "Épico", "lyrical": "<PERSON><PERSON><PERSON><PERSON>", "mystical": "Místico", "melancholy": "Melancolía", "cheerful": "Alegre", "eerie": "Misterioso", "flirtatious": "<PERSON><PERSON><PERSON>", "thoughtful": "Considerado", "cinematic": "Cinematográfico", "humorous": "Humorístico", "instructional": "Instruccional", "conversational": "Conversacional", "apologetic": "Apologé<PERSON><PERSON>", "excuse-making": "Fabricación de excusas", "encouraging": "Fomentando", "neutral": "Neutral", "authoritative": "Autoritativo", "sarcastic cheerful": "Sarcásticamente Alegre", "reassuring": "Tranquilizador", "formal": "Formal", "anguished": "<PERSON><PERSON><PERSON>", "giggling": "Riéndose", "exaggerated": "Exagerado", "cold": "Frío", "hot-tempered": "Impulsivo", "grateful": "Agradecido", "regretful": "Arrepentido", "provocative": "Provocativo", "triumphant": "<PERSON><PERSON><PERSON><PERSON>", "vengeful": "Vengativo", "heroic narration": "Narración heroica", "villainous": "Villanos", "hypnotic": "Hipnótico", "desperate": "Desesperado", "lamenting": "<PERSON><PERSON><PERSON><PERSON>", "celebratory": "Celebratorio", "teasing": "Bromear", "exhausted": "Agotado", "questioning suspicious": "Cuestionando lo sospechoso", "optimistic": "Optimista", "bright, gentle voice, expressing excitement.": "Voz brillante y suave, expresando emoción.", "low, slow voice, conveying deep emotions.": "Voz baja y lenta, transmitiendo emociones profundas.", "sharp, exaggerated voice, expressing frustration.": "Voz aguda y exagerada, expresando frustración.", "fast, lively voice, full of enthusiasm.": "<PERSON>oz rápida y animada, llena de entusiasmo.", "interrupted, joyful voice, interspersed with laughter.": "Voz interrumpida y alegre, intercalada con risas.", "shaky, low voice, expressing pain.": "<PERSON>oz temblorosa y baja, expresando dolor.", "gentle, steady voice, providing reassurance.": "Voz suave y constante, brindando tranquilidad.", "mature, clear voice, suitable for formal content.": "Voz madura y clara, adecuada para contenido formal.", "weary, slightly irritated voice.": "<PERSON>oz cansada, ligeramente irritada.", "bright voice, conveying positivity and hope.": "<PERSON>oz brillante, transmitiendo positividad y esperanza.", "natural, gentle voice with a slow rhythm.": "Voz natural y suave con un ritmo lento.", "lively, engaging voice, captivating for children.": "Voz animada y cautivadora, atractiva para los niños.", "even, slow voice, emphasizing content meaning.": "Voz pausada y tranquila, enfatizando el significado del contenido.", "rhythmic, emotional voice, conveying subtlety.": "Voz rítmica y emotiva, transmitiendo sutileza.", "low, slow voice, evoking curiosity.": "Voz baja y lenta, que evoca curiosidad.", "strong, passionate voice, driving action.": "Voz fuerte y apasionada, impulsando la acción.", "high, interrupted voice, expressing astonishment.": "Voz alta e interrumpida, expresando asombro.", "firm, powerful voice, persuasive and assuring.": "Voz firme, poderosa, persuasiva y tranquilizadora.", "sweet, gentle voice, suitable for emotional content.": "<PERSON>oz dulce y suave, adecuada para contenido emocional.", "shaky, interrupted voice, conveying anxiety.": "Voz temblorosa e interrumpida, transmitiendo ansiedad.", "deep, strong voice with emphasis, creating suspense.": "Voz profunda y fuerte con énfasis, creando suspense.", "engaging, lively voice, emphasizing product benefits.": "Voz atractiva y animada que resalta los beneficios del producto.", "formal, clear voice with focus on key points.": "<PERSON>oz formal, clara y centrada en puntos clave.", "calm, profound voice, delivering authenticity.": "Voz calmada y profunda que transmite autenticidad.", "standard, neutral voice, clear and precise.": "<PERSON><PERSON>, neutral, clara y precisa.", "bright, neutral voice, suitable for concise updates.": "Voz clara y neutral, adecuada para actualizaciones concisas.", "fast, lively voice, stimulating excitement.": "Voz rápida y animada, estimulando la emoción.", "friendly, approachable voice, encouraging engagement.": "Voz amistosa y accesible, alentando la participación.", "empathetic, gentle voice, easy to connect with.": "Voz empática y suave, fácil de conectar.", "clear voice, emphasizing questions and answers.": "<PERSON>oz clara, enfatizando preguntas y respuestas.", "cheerful, playful voice with a hint of mischief.": "Voz alegre y juguetona con un toque de picardía.", "slow, soft voice lacking energy.": "Voz lenta y suave que carece de energía.", "ironic, sharp voice, sometimes humorous.": "<PERSON><PERSON><PERSON><PERSON>, voz aguda, a veces humorística.", "cold voice, clearly expressing discomfort.": "Voz fría, expresando claramente incomodidad.", "soft, mysterious voice, creating intimacy.": "Voz suave y misteriosa, creando intimidad.", "emotional voice, convincing the listener to act.": "Voz emotiva, convenciendo al oyente a actuar.", "gentle voice, evoking feelings of reminiscence.": "<PERSON>oz suave, evocando sentimientos de reminiscencia.", "even, relaxing voice, suitable for mindfulness.": "Voz suave y relajante, adecuada para la atención plena.", "clear voice, emphasizing key words.": "<PERSON><PERSON> clara, enfatizando palabras clave.", "confident, clear voice, ideal for business presentations.": "<PERSON>oz segura y clara, ideal para presentaciones empresariales.", "natural, friendly voice, as if talking to a friend.": "Voz natural y amigable, como si estuvieras hablando con un amigo.", "fast, powerful voice, creating tension and excitement.": "<PERSON><PERSON><PERSON><PERSON>, potente voz, creando tensión y emoción.", "emphasized, suspenseful voice, creating intensity.": "Énfasis, voz suspensiva, creando intensidad.", "professional, formal voice, suitable for business content.": "Voz profesional y formal, adecuada para contenido empresarial.", "energetic, lively voice, introducing new technologies.": "Voz enérgica y animada, introduciendo nuevas tecnologías.", "vibrant, cheerful voice, appealing to younger audiences.": "Voz vibrante y alegre, atractiva para el público más joven.", "gentle, empathetic voice, easing concerns.": "Voz suave y empática, aliviando preocupaciones.", "strong, decisive voice, full of inspiration.": "<PERSON>oz fuerte y decidida, llena de inspiración.", "bright, excited voice, suitable for celebrations.": "Voz brillante y entusiasta, adecuada para celebraciones.", "fast, strong voice, emphasizing urgency.": "<PERSON>oz rápida y fuerte, enfatizando la urgencia.", "passionate, inspiring voice, encouraging action.": "Voz apasionada e inspiradora, alentando a la acción.", "warm, approachable voice, fostering connection.": "Voz cálida y accesible, que fomenta la conexión.", "fast, powerful voice, brimming with enthusiasm.": "Voz rápida y poderosa, rebosante de entusiasmo.", "slow, gentle voice, evoking peace and tranquility.": "Voz lenta y suave, que evoca paz y tranquilidad.", "firm, assertive voice, exuding confidence.": "Voz firme y asertiva, irradiando confianza.", "warm, captivating voice, leaving a strong impression.": "Voz cálida y cautivadora, que deja una fuerte impresión.", "flat, unvaried voice, conveying neutrality or irony.": "Voz plana, invariable, que transmite neutralidad o ironía.", "curious voice, emphasizing questions.": "Voz curiosa, enfatizando preguntas.", "firm, clear voice, guiding the listener step-by-step.": "Voz firme y clara, guiando al oyente paso a paso.", "gentle, slow voice, evoking a floating sensation.": "Voz suave y pausada, evocando una sensación de flotación.", "deep, resonant voice, emphasizing grandeur.": "Voz profunda y resonante, que enfatiza la grandeza.", "soft, melodic voice, similar to singing.": "Voz suave y melódica, similar al canto.", "low, drawn-out voice, evoking mystery.": "<PERSON>oz baja y prolongada, evocando misterio.", "slow, low voice, conveying deep sadness.": "<PERSON>oz lenta y baja, transmitiendo profunda tristeza.", "bright, energetic voice, full of positivity.": "<PERSON>oz brillante y enérgica, llena de positividad.", "low, whispery voice, evoking fear or strangeness.": "<PERSON>oz baja y susurrante, que evoca miedo o extrañeza.", "sweet, teasing voice, full of allure.": "<PERSON>oz dulce y seductora, llena de atractivo.", "slow, reflective voice, full of contemplation.": "<PERSON>oz lenta, reflexiva, llena de contemplación.", "resonant, emphasized voice, creating a movie-like effect.": "Voz resonante y enfatizada, creando un efecto cinematográfico.", "lighthearted, cheerful voice, sometimes exaggerated.": "Voz alegre y jovial, a veces exagerada.", "clear, slow voice, guiding the listener step-by-step.": "Voz clara y pausada, guiando al oyente paso a paso.", "natural voice, as if chatting with the listener.": "Voz natural, como si estuvieras charlando con el oyente.", "soft, sincere voice, expressing regret.": "Voz suave y sincera, expresando arrepentimiento.", "hesitant, uncertain voice, sometimes awkward.": "<PERSON>oz vacilante, incierta, a veces torpe.", "warm voice, providing motivation and support.": "<PERSON>oz cálida, que ofrece motivación y apoyo.", "even voice, free of emotional bias.": "Voz uniforme, libre de sesgo emocional.", "strong, powerful voice, exuding credibility.": "Voz fuerte y poderosa, que irradia credibilidad.", "cheerful voice with an undertone of mockery.": "Voz alegre con un tono de burla.", "gentle, empathetic voice, providing comfort.": "Voz suave y empática, brindando consuelo.", "clear, polite voice, suited for formal occasions.": "Voz clara y cortés, adecuada para ocasiones formales.", "urgent, shaky voice, expressing distress.": "<PERSON><PERSON><PERSON>, voz temblorosa, expresando angustia.", "interrupted voice, mixed with light laughter.": "<PERSON>oz interrumpida, mezclada con una risa ligera.", "loud, emphasized voice, often humorous.": "Voz alta y enfatizada, a menudo humorística.", "flat, unemotional voice, conveying detachment.": "Voz plana, sin emociones, que transmite desapego.", "fast, sharp voice, sometimes out of control.": "<PERSON>oz rá<PERSON>a, agu<PERSON>, a veces fuera de control.", "warm, sincere voice, expressing appreciation.": "<PERSON>oz cálida y sincera, expresando aprecio.", "low, subdued voice, full of remorse.": "<PERSON>oz baja y apagada, llena de remordimiento.", "challenging, strong voice, full of insinuation.": "<PERSON><PERSON><PERSON><PERSON>, voz fuerte, llena de insinuación.", "loud, powerful voice, full of victory.": "<PERSON>oz fuerte y poderosa, llena de victoria.", "low, cold voice, expressing determination for revenge.": "Voz baja y fría, expresando determinación por venganza.", "strong, inspiring voice, emphasizing heroic deeds.": "Voz fuerte e inspiradora, enfatizando hazaña<PERSON> heroicas.", "low, drawn-out voice, full of scheming.": "<PERSON>oz baja y prolongada, llena de intriga.", "even, repetitive voice, drawing the listener in.": "Voz uniforme y repetitiva, atrayendo al oyente.", "urgent, shaky voice, expressing hopelessness.": "<PERSON><PERSON><PERSON>, voz temblorosa, expresando desesperanza.", "low, sorrowful voice, as if mourning.": "Voz baja y triste, como si estuviera de luto.", "excited, joyful voice, full of festive spirit.": "Voz emocionada y alegre, llena de espíritu festivo.", "light, playful voice, sometimes mockingly.": "<PERSON>oz ligera y juguetona, a veces burlona.", "weak, broken voice, expressing extreme fatigue.": "Voz débil y rota, expresando fatiga extrema.", "slow, emphasized voice, full of suspicion.": "<PERSON>oz lenta y enfatizada, llena de sospecha.", "bright, hopeful voice, creating positivity.": "Voz brillante y esperanzadora, creando positividad.", "Your audio will be processed with the latest stable model.": "Su audio será procesado con el modelo estable más reciente.", "Your audio will be processed with the latest beta model.": "Tu audio será procesado con el último modelo beta.", "You don't have any saved prompts yet.": "Todavía no tienes ningún mensaje guardado.", "Commercial Use": "Uso comercial", "Other people’s privacy": "La privacidad de otras personas", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "Debes respetar la privacidad de los demás al utilizar nuestros servicios. No subas ni crees productos de voz que contengan información personal, datos confidenciales o material protegido por derechos de autor sin permiso.", "{price}$ per credit": "{price}$ por crédito", "Pricing": "<PERSON><PERSON><PERSON>", "Simple and flexible. Only pay for what you use.": "Simple y flexible. Solo paga por lo que usas.", "Pay as you go": "Paga según el consumo", "Flexible": "Flexible", "Input characters": "Caracteres de entrada", "Audio model": "Modelo de audio", "Credits": "C<PERSON>dit<PERSON>", "Cost": "Costo", "HD quality voices": "Voces de calidad HD", "Advanced model": "<PERSON><PERSON>", "Buy now": "Compra ahora", "Paste your text to calculate": "Pegue su texto para calcular", "Paste your text here...": "Pega tu texto aquí...", "Calculate": "Calcular", "Estimate your cost by drag the slider below or": "Estime su costo arrastrando el control deslizante a continuación o", "calming": "Tranquilizante", "customer": "Cliente", "exciting": "emocionante", "excuse": "<PERSON><PERSON><PERSON>", "game": "Ju<PERSON>", "hot": "Caliente", "kids": "<PERSON><PERSON><PERSON>", "professional": "Profesional", "tech": "Tecnología", "trailer": "Remolque", "weather": "Clima", "No thumbnail available": "Miniatura no disponible", "Debit or Credit Card": "Tarjeta de débito o crédito", "Visa, Mastercard, American Express and more": "Visa, Mastercard, American Express y más", "Top up now": "<PERSON><PERSON><PERSON> ahora", "noVideoAvailable": "No hay video disponible", "common.back": "Atrás", "common.edit": "<PERSON><PERSON>", "common.save": "Guardar", "common.cancel": "<PERSON><PERSON><PERSON>", "common.delete": "Eliminar", "common.copy": "Copiar", "common.copied": "Copiado", "common.manage": "Gestionar", "aiToolMenu.textToImage": "Texto a imagen", "profileMenu.integration": "Integración", "videoTypes.examples.tikTokDanceTrend": "Tendencia de baile en TikTok", "videoTypes.examples.energeticDanceDescription": "Video de baile enérgico con colores vibrantes, cortes rápidos, música de moda, formato vertical, estilo de redes sociales.", "videoTypes.examples.instagramReel": "<PERSON><PERSON> de Instagram", "videoTypes.examples.lifestyleDescription": "Contenido de estilo de vida con visuales estéticos, transiciones suaves, efectos de moda, narración atractiva.", "videoTypes.examples.comedySketch": "Espectá<PERSON>lo de Comedia", "videoTypes.examples.funnyComedyDescription": "Escena cómica divertida con personajes expresivos, situaciones humorísticas, diálogos entretenidos, ambiente desenfadado", "videoTypes.examples.productLaunchAd": "Anuncio de Lanzamiento de Producto", "videoTypes.examples.professionalCorporateDescription": "Video corporativo profesional con presentación ejecutiva, entorno de oficina limpio, estilo formal de negocios.", "videoTypes.examples.quickPromoVideo": "Vídeo Promocional Rápido", "videoTypes.examples.fastPacedPromoDescription": "Contenido promocional de ritmo rápido con producción eficiente, visuales rentables, mensajes simplificados", "videoTypes.examples.birthdayGreeting": "Saludo de cumpleaños", "videoTypes.examples.personalizedBirthdayDescription": "Video de cumpleaños personalizado con decoraciones festivas, iluminación cálida, ambiente de celebración, mensaje sincero.", "videoTypes.examples.brandStoryVideo": "Video de la historia de la marca", "videoTypes.examples.tutorialVideo": "Video tutorial", "videoTypes.examples.manOnThePhone": "Hombre al teléfono", "videoTypes.examples.runningSnowLeopard": "<PERSON><PERSON><PERSON> de las nieves corriendo", "videoTypes.examples.snowLeopard": "<PERSON><PERSON><PERSON> de las nieves", "videoTypes.styles.cartoonAnimated": "Video de estilo animado o de dibujos animados", "videoTypes.styles.naturalDocumentary": "Metraje de estilo documental natural", "videoTypes.styles.naturalLifelike": "Estilo de video natural y realista", "videoTypes.styles.professionalMovieQuality": "Calidad profesional como de película con iluminación dramática", "videoTypes.styles.creativeStylized": "Efectos de video creativos y estilizados", "videoTypes.styles.retroVintage": "Estética de video retro o vintage", "historyFilter.dialogueGen": "Generación de Diálogos", "historyFilter.speechGenDocument": "Generación de Discurso a partir de Documento", "demo.notifications.availableNotificationTypes": "Tipos de notificación disponibles", "demo.speechVoiceSelect.example1": "Ejemplo 1: <PERSON><PERSON>determinado", "demo.speechVoiceSelect.example2": "Ejemplo 2: <PERSON><PERSON><PERSON> peque<PERSON>", "demo.speechVoiceSelect.example3": "Ejemplo 3: <PERSON><PERSON><PERSON> grande", "demo.speechVoiceSelect.example4": "Ejemplo 4: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> errores", "demo.speechVoiceSelect.example5": "Ejemplo 5: Comparación de estado", "demo.speechVoiceSelect.mainNarrator": "Narrador principal", "demo.speechVoiceSelect.characterVoice": "Voz del personaje", "demo.speechVoiceSelect.selectedVoicesSummary": "Resumen de Voces Seleccionadas:", "demo.speechVoiceSelect.clearAll": "<PERSON><PERSON><PERSON> todo", "demo.speechVoiceSelect.setRandomVoices": "Configurar voces aleatorias", "demo.speechVoiceSelect.logToConsole": "Registrar en la consola", "demo.speechVoiceSelect.notSelected": "No seleccionado", "demo.speechVoiceSelect.voiceSelectionsChanged": "Selección de voz cambiada", "demo.historyWrapper.title": "Demostración de la insignia de estado de HistoryWrapper", "demo.historyWrapper.normalStatus": "Ejemplo 1: Estado Normal (estado = 1)", "demo.historyWrapper.processingStatus": "Ejemplo 2: Estado de procesamiento (estado = 2)", "demo.historyWrapper.errorStatus": "Ejemplo 3: <PERSON><PERSON><PERSON> Error (estado = 3) - Muestra Insignia de Error", "demo.historyWrapper.multipleErrorExamples": "Ejemplo 4: <PERSON><PERSON><PERSON><PERSON> ejemplos de error", "demo.historyWrapper.statusComparison": "Ejemplo 5: Comparación de estados", "demo.historyWrapper.normalImageGeneration": "Generación de imágenes normalizadas", "demo.historyWrapper.videoGenerationInProgress": "Generación de video en progreso", "demo.historyWrapper.speechGenerationFailed": "Generación de Discurso Fallida", "demo.historyWrapper.imageFailed": "<PERSON>n fallida", "demo.historyWrapper.videoFailed": "Video fallido", "demo.historyWrapper.speechFailed": "El discurso falló", "demo.historyWrapper.statusSuccess": "Estado: Éxito", "demo.historyWrapper.statusProcessing": "Estado: Procesando", "demo.historyWrapper.statusError": "Estado: <PERSON><PERSON><PERSON>", "demo.historyWrapper.status1Success": "Estado 1: Éxito", "demo.historyWrapper.status2Processing": "Estado 2: Procesando", "demo.historyWrapper.badgeBehavior": "Comportamiento de Insignia:", "demo.historyWrapper.showsOnlyTypeAndStyle": "Muestra solo insignias de tipo y estilo", "demo.historyWrapper.showsTypeStyleAndError": "Muestra tipo, estilo y distintivo de error rojo con icono de alerta", "demo.historyWrapper.redBackgroundWithWhite": "Fondo rojo con texto blanco y icono de círculo de alerta", "demo.historyWrapper.allBadgesHideOnHover": "Todos los distintivos se ocultan al pasar el ratón para mostrar el contenido superpuesto.", "demo.speechVoiceCaching.title": "Prueba de Cacheo de Voz de Discurso", "demo.speechVoiceCaching.description": "Prueba para verificar la caché de voces entre diferentes componentes.", "demo.speechVoiceCaching.component1Modal": "Componente 1 - Modal", "demo.speechVoiceCaching.component3RegularSelect": "Componente 3 - Selección Regular", "demo.speechVoiceCaching.forceReloadVoices": "Forzar la Recarga de Voces", "demo.speechVoiceCaching.clearAllSelections": "Borrar todas las selecciones", "demo.speechVoiceCaching.logStoreState": "Estado del almacén de registros", "demo.speechVoiceCaching.refreshPageInstructions": "Actualiza la página y abre cualquier componente - se cargará de nuevo", "demo.speechVoiceCaching.checkNetworkTab": "Revisa la pestaña de Red para confirmar las llamadas de API.", "demo.speechVoiceCaching.selectedVoicePersist": "La voz seleccionada se guardará en localStorage.", "demo.speechVoiceCaching.pageMounted": "<PERSON><PERSON><PERSON><PERSON> montada, la tienda se inicializará automáticamente si es necesario.", "integration.title": "Integración", "integration.subtitle": "Administra tus claves API y la configuración de integración", "integration.apiKeys": "Claves de API", "integration.apiKeysDescription": "Administra tus claves API para acceso programático.", "integration.webhook": "Webhook", "integration.webhookDescription": "Configurar la URL del webhook para notificaciones", "apiKeys.title": "Claves API", "apiKeys.subtitle": "Administra tus claves API para acceso programático", "apiKeys.create": "<PERSON><PERSON>r clave de <PERSON>", "apiKeys.createNew": "Crear nueva clave API", "apiKeys.createFirst": "Crear la primera clave API", "apiKeys.name": "Nombre", "apiKeys.nameDescription": "<PERSON> a tu clave API un nombre descriptivo.", "apiKeys.namePlaceholder": "p. ej., Mi Clave API de la Aplicación", "apiKeys.nameRequired": "El nombre de la clave API es obligatorio.", "apiKeys.createdAt": "<PERSON><PERSON><PERSON>", "apiKeys.noKeys": "Sin claves API", "apiKeys.noKeysDescription": "Crea tu primera clave de API para comenzar con el acceso programático.", "apiKeys.created": "Clave API creada con éxito", "apiKeys.createError": "Error al crear la clave API", "apiKeys.deleted": "La clave API se eliminó correctamente.", "apiKeys.deleteError": "No se pudo eliminar la clave de API", "apiKeys.deleteConfirm": "Eliminar clave API", "apiKeys.deleteWarning": "¿Está seguro de que desea eliminar esta clave API? Esta acción no se puede deshacer.", "apiKeys.copied": "Clave API copiada al portapapeles", "apiKeys.copyError": "Error al copiar la clave API", "webhook.title": "Configuración de Webhook", "webhook.subtitle": "Configura la URL del webhook para notificaciones en tiempo real", "webhook.configuration": "URL del webhook", "webhook.currentUrl": "URL actual del webhook", "webhook.currentUrlDescription": "Esta URL recibirá solicitudes POST para eventos de webhook.", "webhook.notConfigured": "No se ha configurado la URL del webhook", "webhook.url": "URL del webhook", "webhook.urlDescription": "Ingrese la URL donde desea recibir notificaciones del webhook", "webhook.urlPlaceholder": "https://tu-dominio.com/webhook", "webhook.urlRequired": "Por favor, ingrese una URL de webhook primero.", "webhook.invalidUrl": "Por favor, introduce una URL válida.", "webhook.saved": "URL de webhook guardado exitosamente", "webhook.saveError": "Error al guardar la URL del webhook", "webhook.test": "Prueba", "webhook.testSent": "Prueba enviada", "webhook.testDescription": "El webhook de prueba se envió correctamente", "webhook.information": "Información del Webhook", "webhook.howItWorks": "Cómo funciona", "webhook.description": "<PERSON>uando esté configurado, enviaremos solicitudes HTTP POST a tu URL de webhook siempre que ocurran ciertos eventos en tu cuenta.", "webhook.events": "Eventos de Webhook", "webhook.imageGenerated": "Generación de imagen completada", "webhook.imageGenerationFailed": "La generación de imágenes falló", "webhook.creditUpdated": "Saldo de crédito actualizado", "webhook.payloadFormat": "Formato de carga útil", "webhook.payloadDescription": "Las solicitudes de webhook se enviarán como JSON con la siguiente estructura:", "webhook.security": "Seguridad", "webhook.securityDescription": "Recomendamos utilizar URLs HTTPS e implementar la verificación de firmas para garantizar la autenticidad del webhook.", "error.general": "Error", "error.validation": "Error de validación", "error.required": "Campo obligatorio", "success.saved": "Guardado con éxito", "success.created": "Creado con éxito", "success.deleted": "Eliminado con éxito", "success.copied": "Copiado al portapapeles", "confirmDelete": "Confirmar eliminación", "confirmDeleteDescription": "¿Estás seguro de que deseas eliminar este elemento? Esta acción no se puede deshacer.", "historyDeleted": "Elemento de historial eliminado con éxito.", "deleteError": "No se pudo eliminar el elemento del historial.", "Regenerate Image": "Regenerar imagen", "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?": "No has realizado ningún cambio en la configuración. ¿Estás seguro de que deseas regenerar la misma imagen?", "Yes, Regenerate": "Sí, regenerar", "Cancel": "<PERSON><PERSON><PERSON>", "models.imagen4Fast": "Imagen 4 Rápida", "models.imagen4Ultra": "Imagen 4 Ultra", "voiceTypes.favoriteVoices": "Voces Favoritas", "voiceTypes.geminiVoices": "Voces de Géminis", "speech.dialogueGeneration.complete": "Generación de diálogo completada", "speech.dialogueGeneration.failed": "Generación de diálogo fallida", "speech.dialogueGeneration.pending": "Generación de Diálogo Pendiente", "speech.dialogueGeneration.dialogueGen": "Generador de Diálogos", "speech.dialogueGeneration.successMessage": "Tu diálogo se ha generado con éxito.", "speech.speechGeneration.complete": "Generación de voz completa", "speech.speechGeneration.failed": "Falló la generación de voz", "speech.speechGeneration.pending": "Generación de Discurso Pendiente", "speech.speechGeneration.successMessage": "Su discurso se ha generado exitosamente.", "speech.speechGeneration.requestWaiting": "Su solicitud de generación de discurso está en espera de ser procesada.", "speech.errors.failedToLoadEmotions": "No se pudo cargar las emociones", "tts-document": "Archivo a Voz", "assignVoicesToSpeakers": "Asignar voces a los hablantes", "speakers": "Altavoces", "addSpeaker": "Agregar orador", "noVoiceAssigned": "No hay voz asignada", "noSpeakersAdded": "Aún no se han a<PERSON><PERSON><PERSON> al<PERSON>.", "assignVoiceToSpeaker": "<PERSON><PERSON><PERSON> voz a {speaker}", "assigned": "<PERSON><PERSON><PERSON>", "assign": "<PERSON><PERSON><PERSON>", "editSpeaker": "<PERSON><PERSON>", "speakerName": "Nombre del orador", "enterSpeakerName": "Ingresar nombre del orador", "save": "Guardar", "speaker": "Altavoz", "assignVoices": "<PERSON><PERSON><PERSON>", "speakersWithVoices": "\"{assigned}/{total} hablantes tienen voces\"", "dialogs": "Diálogos", "addDialog": "<PERSON><PERSON><PERSON>", "enterDialogText": "Ingrese el texto del diálogo...", "selectSpeaker": "Se<PERSON><PERSON><PERSON><PERSON> or<PERSON>", "generateDialogSpeech": "Generar diálogo hablado", "voice 1": "Voz 1", "voice 2": "Voz 2", "uuid": "UUID", "output_format": "Formato de salida", "output_channel": "Canal de salida", "file_name": "Nombre del archivo", "file_size": "Tamaño de archivo", "speakers_count": "Recuento de oradores", "custom_prompt": "Instrucción personalizada", "Please wait a moment...": "Por favor, espere un momento...", "Click to copy": "<PERSON>cer clic para copiar", "Copied to clipboard": "Copiado al portapapeles", "UUID has been copied to clipboard": "El UUID se ha copiado al portapapeles.", "Credits: {credits} remaining": "Créditos: {credits} restantes", "This generation will cost: {cost} Credits": "Esta generación costará: {cost} Créditos", "This generation will cost: {cost} Credits for {duration}s": "Esta generación costará: {cost} Créditos por {duration} segundos", "Your generated video will appear here": "Tu video generado aparecerá aquí.", "Regenerate Video": "Regenerar video", "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?": "No has hecho ningún cambio en la configuración. ¿Estás seguro de que quieres regenerar el mismo video?", "Your generated speech will appear here": "Tu discurso generado aparecerá aquí.", "Regenerate Speech": "Regenerar discurso", "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?": "No has hecho ningún cambio en la configuración. ¿Estás seguro de que quieres regenerar el mismo discurso?", "Generated Speech": "Discurso generado", "Generating speech...": "Generando discurso...", "View Details": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Speech Examples": "Ejemplos de discursos", "Click on any example to use its prompt for speech generation": "Haz clic en cualquier ejemplo para utilizar su indicación para la generación de voz.", "Click to use": "Haz clic para usar", "videoStyles.selectVideoStyle": "Seleccionar estilo de video", "videoStyles.cinematic": "Cinematográfico", "videoStyles.realistic": "Realista", "videoStyles.animated": "Animado", "videoStyles.artistic": "Artístico", "videoStyles.documentary": "Documental", "videoStyles.vintage": "Vintage", "ui.buttons.downloadApp": "Descargar aplicación", "ui.buttons.signUp": "Registrarse", "ui.buttons.viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "ui.buttons.seeLater": "Nos vemos más tarde", "ui.buttons.selectFile": "Seleccionar archivo", "ui.buttons.selectFiles": "Seleccionar archivos", "ui.buttons.pickAVoice": "Elige una voz", "ui.buttons.topUpNow": "<PERSON><PERSON><PERSON> ahora", "ui.buttons.pressEscToClose": "Presiona ESC para cerrar", "ui.labels.clickToCopy": "<PERSON>cer clic para copiar", "ui.labels.copiedToClipboard": "Copiado al portapapeles", "ui.labels.noAudioAvailable": "No hay audio disponible", "ui.labels.noThumbnailAvailable": "Miniatura no disponible", "ui.labels.noPromptAvailable": "No hay mensaje disponible.", "ui.labels.videoModel": "Modelo de Video", "ui.labels.speechModel": "<PERSON><PERSON> de voz", "ui.labels.generatedSpeech": "Discurs<PERSON>", "ui.labels.defaultVoice": "<PERSON><PERSON> predeterminada", "ui.labels.selectAnyVoice": "Selecciona cualquier voz", "ui.labels.cameraMotion": "Movimiento de cámara", "ui.labels.transform": "Transformar", "ui.labels.transforming": "Transformando...", "ui.messages.imageLoaded": "Imagen cargada", "ui.messages.imageRevealComplete": "Revelación de imagen completa", "ui.messages.processingImage": "Procesando imagen", "ui.messages.videoLoaded": "Video cargado", "ui.messages.videoProcessing": "Procesamiento de video", "ui.messages.invalidDownloadLink": "Enlace de descarga no válido", "ui.messages.pleaseSelectSupportedFile": "Por favor, selecciona un archivo compatible", "ui.messages.deleteConfirm": "Confirmar eliminación", "ui.messages.deleteFailed": "Eliminación fallida", "ui.messages.youHaveNewNotification": "Tienes una nueva notificación", "ui.messages.yourGenerationIsReady": "Tu generación está lista", "ui.errors.errorLoadingImage": "Error al cargar la imagen:", "ui.errors.failedToCopy": "Error al copiar: ", "ui.errors.failedToPlayAudioPreview": "No se pudo reproducir la vista previa de audio:", "ui.errors.wavesurferError": "<PERSON><PERSON><PERSON>:", "ui.errors.somethingWentWrong": "Algo salió mal. <PERSON><PERSON> favor, inténtalo de nuevo.", "ui.errors.supabaseUrlRequired": "Se requieren la URL de Supabase y la clave anónima.", "dialog.startTypingHere": "Empieza a escribir el diálogo aquí...", "payment.debitCreditCard": "Tarjeta de débito o crédito", "payment.cardDescription": "Visa, Mastercard, American Express y más", "Style Description": "Descripción del estilo", "Dialog Content": "Contenido del diálogo", "Your generated dialog will appear here": "Tu diálogo generado aparecerá aquí.", "Regenerate Dialog": "Regenerar diálogo", "Generated Dialog": "Diálogo Generado", "Generating dialog...": "Generando diálogo...", "Dialog Information": "Información del diálogo", "Audio Player": "Reproductor de audio", "Voices": "Voces", "Voice 1": "Voz 1", "Voice 2": "Voz 2", "Dialog Examples": "Ejemplos de diálogo", "Click on any example to use its style or dialog content": "Haz clic en cualquier ejemplo para usar su estilo o contenido de diálogo.", "Use Style": "<PERSON>ar estilo", "Use Dialog": "Usar diálogo", "personGeneration": "Generación de Personas", "Imagen": "Imagen", "On": "En", "Off": "<PERSON><PERSON><PERSON>", "Prompts will always be refined to improve output quality": "Los avisos siempre se perfeccionarán para mejorar la calidad del resultado.", "Prompts will not be modified": "Los indicaciones no serán modificadas", "Tips": "Consejos", "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.": "Tu video todavía se está generando en segundo plano. Puedes cerrar esta página y comprobar la pestaña de historial para ver el video generado y te notificaremos cuando esté listo.", "Go to History": "<PERSON><PERSON> a <PERSON>", "footer.youtube": "Youtube", "footer.doctransGPT": "DoctransGPT", "footer.textToSpeechOpenAI": "Texto a Voz OpenAI", "footer.privacyPolicy": "Política de privacidad", "footer.termsOfService": "Términos de servicio", "footer.terms": "Térm<PERSON>s", "footer.privacy": "Privacidad", "Generate": "Generar", "Prompt": "Solicitud", "Generate Video": "Generar video", "ui.errors.generationFailed": "Generación fallida", "downloadVideo": "Des<PERSON><PERSON> video", "imageStyles.selectImageStyle": "Seleccionar estilo de imagen", "imageStyles.none.description": "No se aplicó un estilo específico", "imageStyles.3d-render.description": "Renderizar imagen en 3D", "imageStyles.acrylic.description": "<PERSON><PERSON>r imagen con estilo de pintura acrílica", "imageStyles.anime-general.description": "Generar imagen en estilo anime", "imageStyles.creative.description": "Aplicar efectos artísticos creativos", "imageStyles.dynamic.description": "Crear visuales dinámicos y enérgicos", "imageStyles.fashion.description": "Imagen de estilo para fotografía de moda", "imageStyles.game-concept.description": "Diseño de imagen para arte conceptual de videojuegos", "imageStyles.graphic-design-3d.description": "Aplicar elementos de diseño gráfico en 3D", "imageStyles.illustration.description": "Crear ilustraciones artísticas", "imageStyles.portrait.description": "Optimizar para fotografía de retrato", "imageStyles.portrait-cinematic.description": "<PERSON><PERSON>r estilo de retrato cinematográfico", "imageStyles.portrait-fashion.description": "Aplica un estilo de retrato de moda", "imageStyles.ray-traced.description": "Renderizar con efectos de trazado de rayos", "imageStyles.stock-photo.description": "Crear estilo de foto de stock profesional", "imageStyles.watercolor.description": "Aplicar efectos de pintura en acuarela", "imageStyles.examples": "<PERSON><PERSON><PERSON><PERSON>", "ui.messages.dragDropOrClick": "Arrastra y suelta los archivos aquí o haz clic para seleccionar.", "ui.messages.dropFilesHere": "Suelta archivos aquí", "ui.messages.selectMultipleFiles": "Puede seleccionar varios archivos", "ui.messages.selectSingleFile": "Selecciona un archivo para subir", "ui.messages.supportedFormats": "Formatos compatibles", "ui.messages.releaseToUpload": "Liberar para subir", "ui.labels.generatedAudio": "Audio generado", "ui.actions.showResult": "Mostrar resultado", "ui.actions.hideResult": "Ocultar resultado", "ui.messages.speechGenerating": "Generando habla...", "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.": "Tu discurso todavía se está generando en segundo plano. Puedes cerrar esta página y consultar la pestaña de historial para el audio generado y te notificaremos cuando esté listo.", "downloadAudio": "Descargar audio", "All Countries": "Todos los países", "All Genders": "Todos los géneros", "Country": "<PERSON><PERSON>", "Gender": "<PERSON><PERSON><PERSON>", "Reset": "Restablecer", "Search by name or description...": "Buscar por nombre o descripción...", "Male": "<PERSON><PERSON><PERSON><PERSON>", "Female": "Femenino", "American": "Americano", "British": "Británico", "Australian": "<PERSON><PERSON>", "Indian": "Indio", "Chinese": "Chino", "Spanish": "Español", "Canadian": "Canadiense", "Irish": "Irlandés", "Singaporean": "Singapurense", "Russian": "<PERSON><PERSON><PERSON>", "German": "Alemán", "Portuguese": "Portugués", "Hindi": "Hindi", "Mexican": "Mexicano", "Latin American": "Latinoamericano", "Argentine": "Argentino", "Peninsular": "Peninsular", "French": "<PERSON><PERSON><PERSON><PERSON>", "Parisian": "<PERSON><PERSON><PERSON><PERSON>", "Standard": "<PERSON><PERSON><PERSON><PERSON>", "Brazilian": "Brasilero", "Turkish": "<PERSON><PERSON><PERSON>", "Istanbul": "Estambul", "Bavarian": "Bávara", "Polish": "<PERSON><PERSON><PERSON>", "Italian": "Italiano", "South African": "Sudafricano", "Scottish": "Escocés", "Welsh": "Galés", "New Zealand": "Nueva Zelanda", "Dutch": "Neerlandés", "Belgian": "Belga", "Swedish": "Sueco", "Norwegian": "Noruego", "Danish": "<PERSON><PERSON>", "Korean": "<PERSON><PERSON>", "Korean, Seoul": "Coreano, Seúl", "Japanese": "Japonés", "Croatian": "Croata", "Czech": "Checo", "Moravian": "<PERSON><PERSON><PERSON>", "Zealandic": "Zelandés", "Indonesian": "Indonesio", "Javanese": "Javanés", "Romanian": "<PERSON><PERSON><PERSON>", "Swiss": "Suizo", "Vietnamese": "Vietnamita", "Arabic": "<PERSON><PERSON><PERSON>", "Bulgarian": "Búlgaro", "Finnish": "Finlandés", "Greek": "Griego", "Hungarian": "<PERSON><PERSON><PERSON><PERSON>", "Filipino": "Filipino", "History": "Historia", "imagen-flash": "Gemini 2.5 Flash", "Detail": "Detalle", "Delete": "Eliminar", "ui.errors.unknownError": "Ocurrió un error desconocido.", "ui.errors.tryAgainLater": "Por favor, inténtalo de nuevo más tarde.", "More": "Más", "tts-text": "Audio", "tts-multi-speaker": "Audio", "tts-history": "Audio", "tts-history_1": "Audio", "tts-history_2": "Audio", "tts-history_3": "Audio", "voice-training": "Entrenamiento de Voz", "voice-training_1": "Entrenamiento de Voz", "voice-training_2": "Entrenamiento de Voz", "Start writing or paste your text here or select a file to generate speech...": "Comience a escribir o pegue su texto aquí o seleccione un archivo para generar voz...", "Selecting a voice...": "Seleccionando una voz...", "Voices Library": "Biblioteca de Voces", "Select a voice for your speaker from the library.": "Selecciona una voz para tu altavoz de la biblioteca.", "Next": "Siguient<PERSON>", "Back": "<PERSON><PERSON><PERSON><PERSON>", "Done": "<PERSON><PERSON>", "I got it!": "¡Lo tengo!", "historyPages.endOfHistory": "Has llegado al final de la historia.", "Press ESC to close": "Presiona ESC para cerrar", "Your generated image will appear here": "Tu imagen generada aparecerá aquí.", "Generate Speech": "Generar discurso", "Start writing or paste your text here to generate speech...": "Comienza a escribir o pega tu texto aquí para generar voz...", "Video Gen": "Video Gen", "Generate videos from text prompts and images.": "Generar videos a partir de indicaciones de texto e imágenes.", "Speech Gen": "Generación de discurso", "Convert text and documents to natural speech.": "Convierte texto y documentos a voz natural.", "Dialogue Gen": "Generación de diálogos", "Create natural conversations with multiple speakers.": "Crear conversaciones naturales con múltiples interlocutores.", "Veo 2": "Veo 2", "Text to Video": "Texto a video", "Image to Video": "Imagen a video", "Up to 8 seconds": "Hasta 8 segundos", "1080p Quality": "Calidad 1080p", "Multiple Styles": "<PERSON><PERSON><PERSON><PERSON>", "Text to Speech": "Texto a voz", "Document to Speech": "Documento a Voz", "Multi-Speaker Support": "Soporte multivoces", "50+ Voices": "Más de 50 voces", "Multiple Languages": "<PERSON><PERSON><PERSON><PERSON> idiomas", "Emotion Control": "Control de emociones", "Multi-Speaker Dialogue": "Diálogo multivocal", "Natural Conversations": "Conversaciones Naturales", "Voice Customization": "Personalización de Voz", "Emotion Expression": "Expresión de emociones", "Script Generation": "Generación de guiones", "Audio Export": "Exportación de audio", "Home": "<PERSON><PERSON>", "Price per 1 character: {cost} Credits": "Precio por 1 carácter: {cost} Créditos", "veo-2": "Veo 2", "veo-3": "Veo 3", "Your speech generation is still being generated in the background. You can close this page and check the history tab for the generated speech and we will notify you when it is ready.": "La generación de tu discurso aún se está llevando a cabo en segundo plano. Puedes cerrar esta página y consultar la pestaña de historial para el discurso generado y te notificaremos cuando esté listo.", "Create Another": "C<PERSON>r otra", "estimated_credit": "Cré<PERSON><PERSON> est<PERSON>", "tts-flash": "Gemini 2.5 Flash", "Select Another Voice": "Seleccionar otra voz", "Custom prompt {count}": "Men<PERSON>je personalizado {count}", "Custom prompt": "Instrucción personalizada", "Prompt name": "Nombre del aviso", "This name will help you identify your prompt.": "Este nombre te ayudará a identificar tu aviso.", "Save as new": "Guardar como nuevo", "Ok, save it!": "Está bien, guárdalo.", "Don't use": "No uses", "Use": "<PERSON>ar", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes.": "Tienes el derecho de utilizar la salida de voz generada por nuestros servicios con fines personales, educativos o comerciales.", "Your saved prompts": "Tus indicaciones guardadas", "Success": "Éxito", "Saved prompt successfully": "Solicitud guardada con éxito", "Error": "Error", "Saved prompt failed": "Error al guardar el mensaje", "Updated prompt successfully": "Solicitud actualizada con éxito", "Updated prompt failed": "El mensaje actualizado falló.", "Are you sure you want to delete this prompt?": "¿Estás seguro de que deseas eliminar este mensaje?", "Deleted prompt successfully": "Eliminado el mensaje con éxito.", "Deleted prompt failed": "Solicitud eliminada fallida", "Enter your custom prompt here.": "Introduce tu indicación personalizada aquí.", "Ex: Funny prompt": "Ejemplo: Pregunta divertida", "Discard": "Descar<PERSON>", "Update": "Actualizar", "Edit": "<PERSON><PERSON>", "Custom Prompt": "Indicación personalizada", "Your credits will never expire.": "Tus créditos nunca vencerán.", "Available credits": "Créditos disponibles", "{n}+ Styles": "{n}+ <PERSON><PERSON><PERSON><PERSON>", "Create images from text prompts.": "<PERSON><PERSON><PERSON> imágenes a partir de indicaciones de texto.", "/Image": "/Imagen", "/Video": "/Video", "/1 character": "/1 personaje", "Buy credits": "<PERSON>mp<PERSON>", "My Account": "Mi cuenta", "Manage your account, credits, and orders.": "Administra tu cuenta, créditos y pedidos.", "Full Name": "Nombre completo", "Total Available Credits": "Créditos Totales Disponibles", "Locked Credits": "Créditos Bloqueados", "Save changes": "Guardar cambios", "Your account has been updated.": "Su cuenta ha sido actualizada.", "This field is required.": "Este campo es obligatorio.", "User Info": "Información del Usuario", "Email": "Correo electrónico", "Used to sign in, for email receipts and product updates.": "Usado para iniciar sesión, para recibos por correo electrónico y actualizaciones de productos.", "Active and valid credits only": "Créditos activos y válidos solamente", "We lock your credits to perform transactions.": "Bloqueamos tus créditos para realizar transacciones.", "Referral Link": "<PERSON>lace de referencia", "Share your referral link to earn credits.": "Comparte tu enlace de referido para ganar créditos.", "Referral Code": "Código de referencia", "Your Referral Code": "Tu código de referencia", "Copy": "Copiar", "Copied!": "¡Copiado!", "Orders": "Pedidos", "Manage your orders.": "Administra tus pedidos.", "Will appear on receipts, invoices, and other communication.": "Aparecerá en recibos, facturas y otras comunicaciones.", "User Information": "Información del usuario", "Must be at least 8 characters": "Debe tener al menos 8 caracteres", "Passwords must be different": "Las contraseñas deben ser diferentes", "Passwords must match": "Las contraseñas deben coincidir.", "Current password": "Contraseña actual", "New password": "Nueva contraseña", "Confirm new password": "Confirmar nueva contraseña", "Password": "Contraseña", "Confirm your current password before setting a new one.": "Confirme su contraseña actual antes de establecer una nueva.", "Account": "C<PERSON><PERSON>", "No longer want to use our service? You can delete your account here. This action is not reversible. All information related to this account will be deleted permanently.": "¿Ya no quieres usar nuestro servicio? Puedes eliminar tu cuenta aquí. Esta acción no es reversible. Toda la información relacionada con esta cuenta se eliminará permanentemente.", "Delete account": "Eliminar cuenta", "Change Password": "Cambiar contraseña", "Security": "Seguridad", "Credit Statistics": "Estadísticas de Crédito", "enhance_prompt": "Mejorar indicación", "Current Plan": "Plan actual", "When you buy credits, you will be upgraded to Premium Plan.": "<PERSON><PERSON>do compres créditos, se te actualizará al Plan Premium.", "Available Credits": "Créditos disponibles", "Purchased Credits": "Créditos comprados", "Plan Credits": "Créditos del Plan", "profile.passwordChanged": "Contraseña cambiada", "profile.passwordChangedDescription": "Tu contraseña se ha cambiado con éxito.", "profile.passwordChangeError": "Error al cambiar la contraseña", "profile.passwordChangeErrorDescription": "Hubo un error al cambiar tu contraseña. Por favor, inténtalo de nuevo.", "delete": "Eliminar", "profile.deleteAccount": "Eliminar cuenta", "profile.deleteAccountConfirmation": "¿Estás seguro de que deseas eliminar tu cuenta? Esta acción no se puede deshacer y todos tus datos se perderán permanentemente.", "profile.accountDeleted": "Cuenta eliminada", "profile.accountDeletedDescription": "Tu cuenta ha sido eliminada con éxito.", "profile.accountDeletionError": "Eliminación de cuenta fallida", "profile.accountDeletionErrorDescription": "Hubo un error al eliminar tu cuenta. Inténtalo de nuevo.", "To celebrate our launch, enjoy 50% off select Gemini API models. Offer valid until further notice.": "Para celebrar nuestro lanzamiento, disfruta de un 50% de descuento en modelos seleccionados de Gemini API. Oferta válida hasta nuevo aviso.", "Check now": "Comprueba ahora", "payment.success.title": "¡Pago Exitoso!", "payment.success.message": "¡Gracias por su compra! Su pago ha sido procesado con éxito.", "payment.success.orderId": "ID de pedido:", "payment.success.redirecting": "Redirigiendo a sus pedidos en {seconds} segundos...", "payment.success.viewOrders": "Ver mis pedidos", "payment.error.title": "Error de pago", "payment.error.message": "Hubo un problema al procesar su pago. Por favor, contacte a soporte si esto continúa.", "payment.error.backToOrders": "Volver a los pedidos", "Overview of your credits status.": "Descripción general del estado de tus créditos.", "Payment History": "Historial de pagos", "Your payment history will appear here once you have made a purchase.": "Su historial de pagos aparecerá aquí una vez que haya realizado una compra.", "Payment method": "Método de pago", "Purchase Date": "<PERSON><PERSON>", "Amount": "Cantidad", "Status": "Estado", "Payment amount": "Monto del pago", "payment.status.unavailable": "No disponible", "payment.status.created": "<PERSON><PERSON><PERSON>", "payment.status.completed": "Completado", "payment.status.failed": "Fallido", "payment.status.canceled": "Cancelado", "payment.status.processing": "Procesamiento", "payment.status.refund": "Reembolso", "payment.status.partial_paid": "<PERSON><PERSON><PERSON><PERSON> pagado", "apiKeys.successTitle": "Clave API creada con éxito", "apiKeys.importantNotice": "Aviso importante", "apiKeys.copyWarning": "Esta es la única vez que podrá ver y copiar esta clave de API. Por favor, cópiela ahora y guárdela de forma segura.", "apiKeys.key": "Clave API", "apiKeys.securityTip": "Consejos de Seguridad:", "apiKeys.tip1": "Guarde esta clave en un lugar seguro", "apiKeys.tip2": "Nunca compartas tu clave API públicamente.", "apiKeys.tip3": "Si se ve comprometida, elimine esta clave y cree una nueva.", "apiKeys.copyFirst": "Copiar la clave API primero", "common.done": "<PERSON><PERSON>", "Integration": "Integración", "API Keys": "Claves API", "Manage your API keys.": "Administra tus claves API.", "Create API Key": "Crear clave API", "Name your API key.": "Nombra tu clave API.", "Create": "<PERSON><PERSON><PERSON>", "You have not created any API keys yet.": "Aún no has creado ninguna clave API.", "Copy API Key": "Copiar clave API", "Delete API Key": "Eliminar clave API", "EMAIL_NOT_EXIST": "El correo electrónico no existe", "Your account is not verified": "Tu cuenta no está verificada.", "Your account is not verified. Please verify your account to continue": "Tu cuenta no está verificada. Por favor verifica tu cuenta para continuar.", "TOKEN_USED": "Token ya utilizado", "NOT_ENOUGH_CREDIT": "No hay suficiente crédito. Por favor, recargue su cuenta.", "Not enough credit": "No hay suficiente reconocimiento", "Your account does not have enough credit. Please top up your account to continue.": "Tu cuenta no tiene suficiente crédito. Por favor, recarga tu cuenta para continuar.", "{n} credits": "{n} c<PERSON><PERSON><PERSON>", "USD / {unit}": "USD / {unidad}", "Credits / {unit}": "Créditos / {unit}", "Save {n}%": "Ahorra {n}%", "${price} = {n} credits": "${price} = {n} créditos", "You can switch between money and credits to see the price in your preferred currency.": "<PERSON>uedes alternar entre dinero y créditos para ver el precio en tu moneda preferida.", "Forever": "Para siempre", "For large organizations.": "Para grandes organizaciones.", "Free": "<PERSON><PERSON><PERSON>", "Contact us": "Contáctanos", "Contact sales": "Contactar con ventas", "Premium": "Prima", "Enterprise": "Empresa", "Show money": "Mostrar dinero", "Show credits": "<PERSON>rar c<PERSON>", "Auto upgrade after buy credits": "Actualización automática después de comprar créditos", "Image": "Imagen", "Video": "Vídeo", "Audio": "Audio", "Dialog": "Diálogo", "Get started": "Comenzar", "Contact": "Contacto", "Image Style": "Estilo <PERSON>", "Image Aspect Ratio": "Relación de aspecto de la imagen", "Enhance Prompt": "Mejorar el aviso", "Aspect Ratio": "Relación de aspecto", "Support multiple aspect ratio": "Admite múltiples relaciones de aspecto", "Support enhance prompt": "<PERSON><PERSON><PERSON> mejorar aviso", "Up to {size}MB": "Hasta {size}MB", "Budget Calculator": "Calculadora de presupuesto", "Resource Calculator": "Calculadora de Recursos", "Budget Amount": "Cantidad del Presupuesto", "Resources you can generate:": "Recursos que puedes generar:", "Select resources you want:": "Selecciona los recursos que deseas:", "credits": "c<PERSON><PERSON><PERSON>", "image": "imagen", "video": "video", "per item": "por artí<PERSON>lo", "Quantity": "Cantidad", "Total Cost:": "Costo total:", "Approximately {credits} credits": "Aproximadamente {credits} créditos", "Images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Videos": "Videos", "Pricing Calculator": "Calculadora de Precios", "Calculate how many resources can you generate with your budget.": "<PERSON><PERSON> cuántos recursos puedes generar con tu presupuesto.", "Minimum $10 required": "Se requiere un mínimo de 10 dólares.", "Minimum Purchase Required": "Compra Mínima <PERSON>", "Minimum purchase amount is $10. Please increase your selection.": "El importe mínimo de compra es de $10. Por favor, aumente su selección.", "Minimum purchase amount is $10": "La cantidad mínima de compra es $10.", "Please add more resources to reach the minimum purchase amount.": "Por favor, añade más recursos para alcanzar la cantidad mínima de compra.", "Enter exact number": "Ingrese el número exacto", "Enter budget amount": "Introduzca el monto del presupuesto", "Min: $10": "Mín: $10", "Each amount shows what you can generate with your entire budget (choose one type)": "Cada monto muestra lo que puedes generar con todo tu presupuesto (elige un tipo).", "OR": "O", "AI Image Generation Examples": "Ejemplos de Generación de Imágenes con IA", "Explore the power of AI image generation with these interactive comparisons": "Explora el poder de la generación de imágenes por IA con estas comparaciones interactivas.", "Try the Comparison!": "¡Prueba la Comparación!", "Drag the slider left and right to compare the before and after images. You can also click anywhere on the image to move the slider.": "Arrastra el control deslizante hacia la izquierda y hacia la derecha para comparar las imágenes del antes y el después. También puedes hacer clic en cualquier parte de la imagen para mover el control deslizante.", "Got it!": "¡Entendido!", "Please login to access your saved prompts.": "Inicie sesión para acceder a sus mensajes guardados.", "Access Your Personal Voices": "Accede a tus voces personales", "Access Your Favorite Voices": "Accede a tus voces favoritas", "Login to view and manage your personal voice collection. Upload custom voices and access them anytime.": "Inicia sesión para ver y gestionar tu colección personal de voces. Sube voces personalizadas y accede a ellas en cualquier momento.", "Login to view your favorite voices. Save voices you love and access them quickly for your projects.": "Inicia sesión para ver tus voces favoritas. Guarda las voces que te encantan y accede a ellas rápidamente para tus proyectos.", "Create Account": "<PERSON><PERSON><PERSON> cuenta", "Join thousands of creators using AI voices for their projects": "Únete a miles de creadores que utilizan voces de IA para sus proyectos.", "AI Video Generation Examples": "Ejemplos de generación de video con IA", "Explore the power of AI video generation with these interactive comparisons": "Explora el poder de la generación de videos con IA con estas comparaciones interactivas.", "Try the Video Comparison!": "¡Prueba la Comparación de Videos!", "Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider.": "Desliza el control deslizante hacia la izquierda y hacia la derecha para comparar indicaciones de texto/imágenes con videos generados. También puedes hacer clic en cualquier lugar para mover el control deslizante.", "Duration": "Duración", "Select video duration in seconds": "Selecciona la duración del video en segundos", "This setting is locked for the selected model": "Esta configuración está bloqueada para el modelo seleccionado.", "Prompts will always be refined to improve output quality (required for this model)": "Los indicios siempre se refinarán para mejorar la calidad de salida (requerido para este modelo)", "SIGNUP_MAIL_EXIST": "El correo electrónico ya existe", "SIGNIN_USER_NOT_FOUND": "Usuario no encontrado", "SIGNIN_USER_NOT_VERIFIED": "Usuario no verificado", "SIGNIN_USER_DISABLED": "<PERSON><PERSON><PERSON> deshabili<PERSON>o", "SIGNIN_WRONG_PASSWORD": "Contrase<PERSON>", "SIGNIN_USER_NOT_FOUND_FOR_EMAIL": "Usuario no encontrado para el correo electrónico", "SIGNIN_INVALID_EMAIL": "Correo electrónico no válido", "auth.accountCreated": "<PERSON><PERSON>ta creada", "auth.accountCreatedDescription": "Su cuenta ha sido creada con éxito. Por favor, revise su correo electrónico para verificar su cuenta.", "Select voice on right": "Selecciona la voz a la derecha", "privacy.lastUpdated": "Última actualización:", "privacy.lastUpdatedDate": "15 de enero de 2025", "privacy.introduction": "En GeminiGen.AI, damos prioridad a la protección de su privacidad y la seguridad de su información personal. Esta política de privacidad describe cómo recopilamos, utilizamos y protegemos la información que usted proporciona al utilizar nuestros servicios de generación de contenido de IA, incluyendo la generación de imágenes, generación de videos, síntesis de voz y generación de diálogos. Al acceder y utilizar nuestro sitio web (geminigen.ai), usted da su consentimiento a las prácticas descritas en esta política.", "privacy.informationCollectionDescription": "Cuando creas una cuenta en nuestro sitio web, recopilamos cierta información personal como tu dirección de correo electrónico y nombre completo. Esta información es necesaria para otorgarte acceso a nuestros servicios, proporcionar actualizaciones o cambios en nuestros servicios, y para análisis estadísticos que mejoren nuestras ofertas. Además, cualquier texto, imagen o documento cargado para la generación de contenido por IA se almacena temporalmente únicamente con el propósito de generar el resultado.", "privacy.creditCalculation": "2. <PERSON><PERSON><PERSON><PERSON><PERSON>ré<PERSON>", "privacy.creditCalculationDescription": "Para garantizar una facturación precisa, el número de créditos necesarios para la generación de contenido de IA se calcula en función del texto, imágenes o documentos proporcionados. Este cálculo se realiza utilizando nuestro algoritmo patentado y es directamente proporcional a la complejidad y longitud de la entrada.", "privacy.paymentSecurity": "3. <PERSON><PERSON> y Seguridad", "privacy.paymentSecurityDescription": "Para el procesamiento de pagos, ofrecemos opciones de PayPal y tarjeta de crédito. No almacenamos información de tarjetas de crédito en nuestros servidores. Todas las transacciones de pago son manejadas de manera segura por proveedores de servicios de pago de terceros de confianza, en cumplimiento con sus respectivas políticas de privacidad y seguridad.", "privacy.emailNotification": "4. Notificación por correo electrónico y acceso a contenido generado", "privacy.emailNotificationDescription": "Al completar la generación de contenido, recibirás una notificación por correo electrónico que contiene un enlace seguro para acceder y descargar el resultado generado (imágenes, videos, archivos de audio o diálogo). Este enlace permanecerá activo durante un período de tiempo específico para tu conveniencia.", "privacy.thirdPartyServices": "6. <PERSON><PERSON><PERSON>", "privacy.thirdPartyServicesDescription": "Podemos utilizar servicios de terceros, como proveedores de análisis, para mejorar nuestros servicios y analizar los patrones de uso. Estos servicios pueden recopilar información sobre su uso, pero no tienen acceso a su información personal.", "privacy.cookies": "7. Cookies y tecnologías de rastreo", "privacy.cookiesDescription": "Nuestro sitio web utiliza cookies y tecnologías de seguimiento similares para mejorar la experiencia del usuario y analizar el uso del sitio web. Tienes la opción de desactivar las cookies a través de la configuración de tu navegador, pero ten en cuenta que algunas funciones de nuestro sitio web pueden no funcionar correctamente como resultado.", "privacy.thirdPartyLinks": "8. <PERSON><PERSON><PERSON>", "privacy.thirdPartyLinksDescription": "Nuestro sitio web puede contener enlaces a sitios web de terceros. No somos responsables de las prácticas de privacidad o del contenido de estos sitios web y le animamos a revisar sus respectivas políticas de privacidad.", "privacy.childrenPrivacy": "9. <PERSON>riva<PERSON><PERSON> de los niños", "privacy.childrenPrivacyDescription": "Nuestros servicios no están destinados a personas menores de 18 años y no recopilamos ni almacenamos intencionadamente información personal de personas menores de esta edad. Si llegamos a saber de la recopilación involuntaria de información personal de un niño menor de 18 años, tomaremos medidas para eliminar dicha información de nuestros registros.", "privacy.policyChanges": "Actualizaciones de nuestra Política de Privacidad", "privacy.policyChangesDescription": "Podemos actualizar periódicamente nuestra Política de Privacidad para reflejar cambios en las prácticas o requisitos legales. Cualquier revisión será efectiva inmediatamente después de publicar la política actualizada en nuestro sitio web. Le alentamos a revisar periódicamente esta Política de Privacidad para obtener la información más reciente.", "privacy.commercialUse": "11. <PERSON><PERSON>", "privacy.commercialUseDescription": "Tienes el derecho de usar el contenido generado por nuestros servicios para fines personales, educativos o comerciales. Sin embargo, no puedes revender, redistribuir ni sublicenciar el contenido generado sin el consentimiento previo por escrito de GeminiGen.AI.", "privacy.otherPeoplePrivacy": "12. La privacidad de otras personas", "privacy.otherPeoplePrivacyDescription": "Debes respetar la privacidad de los demás al usar nuestros servicios. No subas ni crees contenido que contenga información personal, datos confidenciales o material protegido por derechos de autor sin permiso.", "privacy.unsubscribe": "13. <PERSON><PERSON><PERSON>", "privacy.unsubscribeDescription": "Puedes optar por no recibir publicidad personalizada haciendo clic en el botón 'Toggle' en la configuración de tu perfil.", "terms.lastUpdated": "Última actualización:", "terms.lastUpdatedDate": "15 de enero de 2025", "terms.introduction": "Bienvenido a GeminiGen.AI. Estos Términos de Servicio rigen el uso de nuestros servicios de generación de contenido impulsados por IA, incluyendo generación de imágenes, generación de videos, síntesis de voz y generación de diálogos.", "terms.acceptanceOfTermsDetails": "Su uso continuo de nuestros servicios constituye la aceptación de cualquier cambio en estos Términos.", "terms.serviceDescription": "2. Descripción de los servicios", "terms.serviceDescriptionText": "GeminiGen.AI ofrece servicios de generación de contenidos impulsados por IA, que incluyen, entre otros:", "terms.serviceUsageDescription": "Se compromete a emplear nuestros servicios únicamente para fines legales. Deberá abstenerse de cargar, transmitir o almacenar cualquier contenido que sea ilegal, da<PERSON><PERSON>, difamatorio o que infrinja los derechos de otros. Usted es el único responsable de cualquier contenido enviado para la generación de contenido de IA.", "terms.permittedUse": "Uso permitido 4.1", "terms.permitted1": "Utilice nuestros servicios para fines legales, creativos y comerciales.", "terms.permitted2": "Generar contenido que cumpla con las leyes y regulaciones aplicables", "terms.permitted3": "Respeta los derechos de propiedad intelectual de los demás", "terms.permitted4": "Utilice contenido generado de acuerdo con nuestros términos de licencia.", "terms.prohibitedUse": "Uso Prohibido 4.2", "terms.prohibited1": "Generar contenido que sea ilegal, da<PERSON><PERSON>, amena<PERSON><PERSON>, abusivo o discriminatorio", "terms.prohibited2": "<PERSON><PERSON>r contenido que infrinja los derechos de propiedad intelectual de otros", "terms.prohibited3": "Producir contenido destinado a engañar, defraudar o inducir a error a otros.", "terms.prohibited4": "Generar contenido que represente a menores en situaciones inapropiadas", "terms.prohibited5": "<PERSON><PERSON>r contenido que promueva la violencia, el terrorismo o actividades ilegales", "terms.prohibited6": "Usa nuestros servicios para enviar spam, acosar o dañar a otros.", "terms.prohibited7": "Intentar realizar ingeniería inversa, hackear o comprometer nuestros sistemas", "terms.prohibited8": "Violar cualquier ley o regulación aplicable", "terms.userAccounts1": "Proporcionar información de registro precisa y completa", "terms.userAccounts2": "Mantener la seguridad y confidencialidad de las credenciales de su cuenta", "terms.userAccounts3": "Todas las actividades que ocurren bajo tu cuenta", "terms.userAccounts4": "Notificarnos de inmediato sobre cualquier uso no autorizado de su cuenta.", "terms.userAccounts5": "Asegurarse de que su información de cuenta se mantenga actualizada y precisa", "terms.paymentAndBilling": "3. <PERSON><PERSON> y Crédit<PERSON>", "terms.paymentAndBillingDescription": "Nuestros servicios de generación de contenido de IA operan en un sistema basado en créditos. El número de créditos necesarios para la creación de contenido se determina mediante nuestro algoritmo propietario y se calcula con precisión en función de la complejidad de la entrada y los requisitos de salida.", "terms.payment1": "Al agotar el saldo de su crédito, debe recargar su cuenta.", "terms.payment2": "Los pagos se pueden realizar mediante PayPal o tarjeta de crédito.", "terms.payment3": "Todos los pagos se procesan de forma segura a través de procesadores de pago de terceros.", "terms.payment4": "Los créditos no son reembolsables excepto cuando lo exija la ley aplicable.", "terms.payment5": "Los precios están sujetos a cambios con previo aviso razonable.", "terms.payment6": "Usted es responsable de todos los impuestos y tarifas aplicables.", "terms.ourIntellectualProperty": "5.1 Nuestra Propiedad Intelectual", "terms.ourIntellectualPropertyDescription": "GeminiGen.AI y sus servicios, incluidos todos los programas, algoritmos, diseños y contenido, están protegidos por leyes de propiedad intelectual. No puede copiar, modificar, distribuir ni crear obras derivadas sin nuestro permiso expreso por escrito.", "terms.userGeneratedContent": "Contenido Generado por el Usuario 5.2", "terms.userGeneratedContentDescription": "Usted conserva la propiedad del contenido que crea utilizando nuestros servicios, sujeto a lo siguiente:", "terms.userContent1": "Nos otorgas una licencia limitada para procesar y almacenar tu contenido para proporcionar nuestros servicios.", "terms.userContent2": "Usted declara que tiene derecho a utilizar cualquier contenido de entrada que proporcione.", "terms.userContent3": "Usted es responsable de garantizar que su contenido generado cumpla con estos Términos.", "terms.userContent4": "Podemos eliminar contenido que viole nuestras políticas o las leyes aplicables.", "terms.privacyAndDataProtection": "7. Privacidad y Protección de Datos", "terms.privacyAndDataProtectionDescription": "Tu privacidad es importante para nosotros. Nuestra recopilación, uso y protección de tu información personal se rige por nuestra Política de Privacidad, que está incorporada a estos Términos por referencia.", "terms.serviceAvailability": "8. Disponibilidad del servicio", "terms.serviceAvailabilityDescription": "Si bien nos esforzamos por proporcionar servicios confiables, no garantizamos el acceso ininterrumpido. Nuestros servicios pueden estar temporalmente no disponibles debido a mantenimiento, actualizaciones o problemas técnicos. Nos reservamos el derecho de modificar o descontinuar los servicios con un aviso razonable.", "terms.terminationByUser": "Terminación por Usted 8.1", "terms.terminationByUserDescription": "Puedes cancelar tu cuenta en cualquier momento contactando a nuestro equipo de soporte. Al cancelar, tu acceso a nuestros servicios cesará, pero estos Términos seguirán aplicándose a tu uso anterior de nuestros servicios.", "terms.terminationByUs": "8.2 Terminación por nosotros", "terms.terminationByUsDescription": "Podemos suspender o cancelar su cuenta y el acceso a nuestros servicios de forma inmediata, con o sin previo aviso, por cualquiera de las siguientes razones:", "terms.termination1": "Violación de estos Términos de Servicio", "terms.termination2": "Actividad fraudulenta, abusiva o ilegal", "terms.termination3": "Impago de tarifas o cargos", "terms.termination4": "Períodos prolongados de inactividad", "terms.termination5": "Requisitos legales o normativos", "terms.termination6": "Protección de nuestros derechos, propiedad o seguridad", "terms.limitationOfLiability": "Limitación de Responsabilidad", "terms.limitationOfLiabilityDescription": "GeminiGen.AI no será responsable de ningún daño directo, indirecto, incidental, especial o consecuente que surja de o esté relacionado con su uso de nuestros servicios. No garantizamos la exactitud, integridad o disponibilidad de los servicios y rechazamos todas las garantías, ya sean expresas o implícitas, relacionadas con su uso o resultados.", "terms.disclaimer1": "Garantías de comerciabilidad, idoneidad para un propósito particular y no infracción", "terms.disclaimer2": "Garantías sobre la exactitud, fiabilidad o calidad del contenido generado.", "terms.disclaimer3": "Responsabilidad por cómo utilizas o distribuyes contenido generado", "terms.disclaimer4": "Responsabilidad por cualquier daño resultante de interrupciones del servicio o problemas técnicos", "terms.indemnification": "12. Indemnización", "terms.indemnificationDescription": "<PERSON><PERSON><PERSON>, defender y mantener indemne a GeminiGen.AI y sus afiliados de cualquier reclamación, da<PERSON>, pérdida o gasto que surja de tu uso de nuestros servicios, violación de estos Términos o infracción de derechos de terceros.", "terms.governingLaw": "9. <PERSON><PERSON> Aplicable", "terms.governingLawDescription": "Estos Términos de Servicio se interpretarán y regirán de acuerdo con las leyes de Vietnam, sin tener en cuenta sus principios de conflicto de leyes. Cualquier disputa que surja de o en relación con estos Términos y la utilización de nuestros servicios estará sujeta a la jurisdicción exclusiva de los tribunales en Vietnam.", "terms.clarificationOpenAI": "Aclaración sobre los servicios de IA de terceros", "terms.clarificationOpenAIDescription": "GeminiGen.AI es una entidad independiente y no está afiliada a OpenAI, Google u otros proveedores de servicios de IA. Nuestros servicios de generación de contenido utilizan varias API de IA para convertir texto en imágenes, videos, voz y diálogo, pero operamos de manera independiente de estos proveedores. Esta aclaración se hace para evitar cualquier confusión o malentendido con respecto a la relación entre GeminiGen.AI y los proveedores de servicios de IA de terceros. Los usuarios deben ser conscientes de que, si bien utilizamos tecnología de IA de terceros para ofrecer nuestros servicios, GeminiGen.AI es el único responsable del funcionamiento de nuestros servicios y del cumplimiento de estos Términos de Servicio.", "terms.contactEmail": "Correo electrónico:", "terms.contactAddress": "Dirección:", "terms.companyAddress": "El sitio web es operado conjuntamente por A2ZAI LTD No:16078579 Dirección registrada en 483 Green Lanes, London, England, N13 4BS", "footer.supportEmail": "Soporte por email", "footer.address": "Dirección", "footer.companyAddress": "El sitio web es operado conjuntamente por A2ZAI LTD No:16078579 Dirección registrada en 483 Green Lanes, London, England, N13 4BS", "second": "segundo", "1M characters": "1 millón de caracteres", "Support {n}+ voices": "Soporte para {n}+ voces", "Support {n}+ emotions": "Apoyar {n}+ emociones", "Support {n}+ languages": "Admite {n}+ idiomas", "Support custom prompt": "Apoyar indicaciones personalizadas", "Support MP3 and WAV": "Compatibilidad con MP3 y WAV", "Support speed control": "Control de velocidad de apoyo", "Support document to speech": "Documento de apoyo al discurso", "Current plan": "Plan actual", "Already premium": "Ya es premium", "Gemini 2.5 Flash": "Gemini 2.5 Flash", "Gemini 2.5 Pro": "Gemini 2.5 Pro", "Voice": "Voz", "Emotion": "Emoción", "Language": "Idioma", "Output Format": "Formato de salida", "Speed": "Velocidad", "Document": "Documento", "Enjoy 50% off select Gemini API models. Offer valid until further notice.": "Disfruta de un 50% de descuento en modelos selectos de la API Gemini. Oferta válida hasta nuevo aviso.", "Text must be at least 4 characters long.": "El texto debe tener al menos 4 caracteres de longitud.", "Each dialog text must be at least 4 characters long.": "Cada texto de diálogo debe tener al menos 4 caracteres de longitud.", "Please enter text or select a file to generate speech.": "Por favor, ingresa texto o selecciona un archivo para generar voz.", "Please select a voice for speech generation.": "Por favor, seleccione una voz para la generación de habla.", "Please add at least one dialog to generate speech.": "Por favor, añade al menos un diálogo para generar voz.", "Please select voices for both speakers.": "Por favor, selecciona las voces para ambos hablantes.", "Photorealistic": "Fotorrealista", "imageStyles.photorealistic.description": "Imagen realista con alto detalle y resolución", "Delete Voice": "Eliminar voz", "Are you sure you want to delete this voice? This action cannot be undone.": "¿Estás seguro de que deseas eliminar esta voz? Esta acción no se puede deshacer.", "Voice deleted successfully": "Voz eliminada con éxito", "Failed to delete voice": "Error al eliminar la voz", "Create your first voice": "<PERSON>rea tu primera voz", "Create Custom Voice": "<PERSON><PERSON><PERSON> voz personalizada", "Type of voice to create": "Tipo de voz para crear", "Instant Voice Cloning": "Clonación instantánea de voz", "Clone a voice from a clean sample recording. Samples should contain 1 speaker and be over 1 minute long and not contain background noise": "Clona una voz a partir de una muestra de grabación limpia. Las muestras deben contener 1 hablante, durar más de 1 minuto y no contener ruido de fondo.", "Professional Voice Cloning": "Clonación de Voz Profesional", "Create the most realistic digital replica of your voice.": "Crea la réplica digital más realista de tu voz.", "Speaker Name": "Nombre del Ponente", "Enter speaker name": "Ingrese el nombre del orador", "Describe the voice characteristics": "Describe las características de la voz", "Select gender": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Select age": "Seleccionar edad", "Select accent": "Se<PERSON><PERSON>onar acento", "Audio Sample": "Muestra de audio", "I agree to the privacy policy": "Acepto la política de privacidad", "Note:": "Nota:", "The sound should be clear, without any noise, and last around 1 minutes to ensure good quality.": "El sonido debe ser claro, sin ningún ruido, y durar alrededor de 1 minuto para asegurar buena calidad.", "It will cost 0 credits each time you create a voice.": "Costará 0 créditos cada vez que crees una voz.", "Young": "<PERSON><PERSON>", "Middle": "Medio", "Old": "Viejo", "English": "Inglés", "Custom voice created successfully": "Voz personalizada creada con éxito", "Failed to create custom voice": "Error al crear voz personalizada", "validation.mustAgreeToPrivacy": "Debes aceptar la política de privacidad", "I agree to the {0}": "Estoy de acuerdo con el {0}", "Privacy Policy": "Política de Privacidad", "The sound should be clear, without any noise, and last around 10 minutes to ensure good quality.": "El sonido debe ser claro, sin ningún ruido, y durar alrededor de 10 minutos para garantizar una buena calidad.", "It will cost 5,000 credits each time you create a voice.": "Costará 5,000 créditos cada vez que crees una voz.", "Maximum file size": "Tamaño máximo de archivo", "File size exceeds maximum limit of {maxSize}": "El tamaño del archivo excede el límite máximo de {maxSize}", "File size exceeds 150MB limit": "El tamaño del archivo excede el límite de 150 MB.", "ui.errors.viewGooglePolicy": "Ver la Política de Uso de IA Generativa de Google", "negativePrompt": "Negative Prompt", "negativePromptDescription": "Describe what you don't want to see in the video", "negativePromptTooltip": "Negative prompts help exclude unwanted elements, styles, or concepts from your video generation. For example: 'blurry, low quality, distorted faces'", "negativePromptPlaceholder": "Enter what you want to avoid in the video...", "negativePromptSuggestions": "Quick suggestions", "negativePromptSuggestion1": "blurry, low quality", "negativePromptSuggestion2": "distorted faces, deformed", "negativePromptSuggestion3": "text, watermark", "negativePromptSuggestion4": "dark, underexposed", "negativePromptSuggestion5": "shaky, unstable motion", "negativePromptSuggestion6": "pixelated, artifacts", "negative_prompt": "Instrucción negativa", "aspect_ratio": "Relación de aspecto", "person_generation": "Generación de Personas", "ALLOW_ADULT": "<PERSON><PERSON><PERSON>", "ALLOW_ALL": "<PERSON><PERSON><PERSON>", "veo-3-fast": "Veo 3 Rápido", "Click to copy. UUID is unique and can be used to contact support.": "Haz clic para copiar. El UUID es único y puede utilizarse para contactar con el soporte.", "Account Activation Required": "Activación de cuenta requerida", "Please activate your account to access your personal voice collection. Check your email for the activation link.": "Por favor, active su cuenta para acceder a su colección personal de voces. Revise su correo electrónico para el enlace de activación.", "Please activate your account to access your favorite voices. Check your email for the activation link.": "Por favor, active su cuenta para acceder a sus voces favoritas. Revise su correo electrónico para encontrar el enlace de activación.", "Resend Activation Email": "Reenviar correo electrónico de activación", "Check Email": "Revisar correo electrónico", "Didn't receive the email? Check your spam folder or try resending.": "¿No recibiste el correo electrónico? Revisa tu carpeta de spam o intenta reenviarlo.", "Activation email sent": "Correo de activación enviado", "Please check your email for the activation link.": "Por favor, revisa tu correo electrónico para encontrar el enlace de activación.", "Voice Training Started": "Comenzó el entrenamiento de voz", "Your custom voice is being trained. You will be notified when it's ready.": "Se está entrenando tu voz personalizada. Se te notificará cuando esté lista.", "Voice Training Complete": "Entrenamiento de voz completado", "Your custom voice is ready to use!": "¡Tu voz personalizada está lista para usar!", "Voice Training Failed": "Entrenamiento de voz fallido", "Voice training failed. Please try again.": "El entrenamiento de voz ha fallado. Por favor, inténtelo de nuevo.", "Training": "Entrenamiento", "Failed": "Fallido", "Voice Training in Progress": "Entrenamiento de voz en progreso", "Your custom voice is being trained. This process may take up to 30 minutes. You will be notified when it's ready.": "Tu voz personalizada está siendo entrenada. Este proceso puede tardar hasta 30 minutos. Se te notificará cuando esté lista.", "Voice Name": "Nombre de voz", "Training Type": "Tipo de Entrenamiento", "Training Progress": "Progreso del entrenamiento", "Estimated time: 5-30 minutes": "Tiempo estimado: 5-30 minutos", "Refresh Status": "Actualizar estado", "Voice Not Available": "Voz no disponible", "This voice is currently being trained and cannot be selected yet.": "Esta voz está siendo entrenada actualmente y aún no puede ser seleccionada.", "hero.title": "Crea contenido increíble de IA", "hero.subtitle": "Transforma tus ideas en impresionantes imágenes, videos, voz y más generados por IA. Experimenta el futuro de la generación de contenido creativo.", "hero.cta.primary": "Empieza a crear", "hero.cta.secondary": "Ver demostración", "hero.users": "Con la confianza de más de 10,000 creadores en todo el mundo.", "hero.scroll": "Desplázate para explorar", "features.title": "Características principales", "features.subtitle": "Descubre potentes funciones que te ayudan a crear contenido profesional de IA.", "features.ai.title": "Inteligencia artificial avanzada", "features.ai.description": "Utiliza tecnología de IA de vanguardia para generar contenido de alta calidad con una precisión asombrosa.", "features.speed.title": "Generación Rápida", "features.speed.description": "Transforma tus ideas en contenido en solo segundos. Sin largos tiempos de espera.", "features.creative.title": "Creatividad Ilimitada", "features.creative.description": "Crea contenido en cualquier estilo, desde animación hasta realista, desde artístico hasta profesional.", "features.quality.title": "Alta calidad", "features.quality.description": "Salida con alta resolución, movimiento fluido y detalles nítidos.", "features.collaboration.title": "Colaboración sencilla", "features.collaboration.description": "Comparte y colabora en proyectos con tu equipo fácilmente.", "features.export.title": "Exportación multiformato", "features.export.description": "Exportar contenido en varios formatos adecuados para todas las plataformas y propósitos.", "features.cta.title": "¿Listo para comenzar?", "features.cta.description": "Experimenta el poder de la IA en la creación de contenido hoy.", "features.cta.button": "Crea tu primer contenido", "howto.title": "Cómo crear contenido de IA", "howto.subtitle": "Con solo 4 sencillos pasos, puedes crear contenido profesional con inteligencia artificial.", "howto.step1.title": "Describe tu idea", "howto.step1.description": "Escribe una descripción detallada del contenido que deseas crear. Incluye escenas, personajes, acciones y estilo.", "howto.step2.title": "Personalizar configuración", "howto.step2.description": "Elige la resolución, la relación de aspecto, el estilo y otros parámetros adecuados para tu propósito.", "howto.step3.title": "Procesamiento de IA", "howto.step3.description": "La inteligencia artificial analizará tu solicitud y creará contenido de alta calidad en segundos.", "howto.step4.title": "Descargar y Compartir", "howto.step4.description": "Descarga contenido a tu computadora o compártelo directamente en tus plataformas de redes sociales favoritas.", "howto.examples.title": "Ejemplos de Contenido de IA", "howto.examples.subtitle": "Ver contenido creado a partir de indicaciones simples", "howto.cta": "Intenta crear ahora", "faq.title": "Preguntas Frecuentes", "faq.description": "Encuentra respuestas a preguntas comunes sobre la creación de contenido de IA", "faq.general.title": "General", "faq.pricing.title": "<PERSON><PERSON><PERSON>", "faq.contact.title": "¿Aún tienes preguntas?", "faq.contact.description": "Nuestro equipo de soporte está listo para ayudarte las 24 horas del día, los 7 días de la semana.", "faq.contact.email": "Enviar correo electrónico", "faq.contact.discord": "Únete a Discord", "footer.product.title": "Producto", "footer.product.features": "Características", "footer.product.howto": "Cómo utilizar", "footer.product.pricing": "<PERSON><PERSON><PERSON>", "footer.product.app": "Aplicación", "footer.company.title": "Empresa", "footer.company.about": "Sobre nosotros", "footer.company.blog": "Blog", "footer.company.careers": "Carr<PERSON>s", "footer.company.contact": "Contacto", "footer.legal.title": "Legal", "footer.legal.privacy": "Política de Privacidad", "footer.legal.terms": "Términos de servicio", "footer.legal.cookies": "Política de cookies", "footer.support.title": "Apoyo", "footer.support.help": "Centro de ayuda", "footer.support.api": "Documentación de la API", "footer.support.status": "Estado del sistema", "footer.description": "Crea contenido de IA de alta calidad con la tecnología más avanzada. Transforma ideas en contenido profesional en segundos.", "footer.newsletter.title": "Obtén las últimas actualizaciones", "footer.newsletter.description": "Suscríbete para recibir noticias sobre nuevas funciones y actualizaciones de productos.", "footer.newsletter.placeholder": "Introduce tu correo electrónico", "footer.newsletter.subscribe": "Suscribirse", "footer.copyright": "© 2024 GeminiGen AI. Todos los derechos reservados.", "footer.language": "Inglés", "Features That Set Us Apart": "Características que nos diferencian", "howto.examples.tryPrompt": "Prueba este aviso", "howto.examples.example1.title": "Gato Flor de Cerezo", "howto.examples.example1.prompt": "Un gato corriendo por un campo de cerezos en flor al atardecer.", "howto.examples.example2.title": "<PERSON><PERSON> <PERSON>", "howto.examples.example2.prompt": "Un hombre surfeando sobre las olas azules del océano en un hermoso día soleado.", "howto.examples.example3.title": "Ciudad del Futuro", "howto.examples.example3.prompt": "Una ciudad futurista con autos voladores y rascacielos imponentes", "howto.examples.example4.title": "Danza cultural", "howto.examples.example4.prompt": "Bailarines tradicionales actuando en trajes coloridos en un festival", "howto.examples.example5.title": "<PERSON><PERSON><PERSON>", "howto.examples.example5.prompt": "Un sereno paisaje montañoso con ríos caudalosos y picos brumosos", "videoGen.imageToVideo": "Imagen a video", "videoGen.textToVideo": "Texto a video", "videoGen.generated": "Generado", "videoGen.imageToVideoGenerated": "Imagen a video generado", "videoGen.textToVideoGenerated": "Texto a video generado", "videoGen.generationStarted": "Estamos comenzando a generar tu video, por favor espera unos 2 minutos...", "videoGen.imageToVideoGenerationStarted": "Estamos comenzando a generar tu video a partir de la imagen, por favor espera unos 2 minutos...", "videoGen.textToVideoGenerationStarted": "Estamos comenzando a generar tu video a partir del texto, por favor espera unos 2 minutos...", "videoGen.pleaseEnterPrompt": "Por favor, ingrese un mensaje para generar un video.", "videoGen.cinematic": "Cinematográfico", "videoGen.model": "<PERSON><PERSON>", "videoGen.imageReference": "Referencia de imagen", "videoGen.prompt": "Solicitud", "videoGen.negativePrompt": "Indicador negativo", "videoGen.negativePromptDescription": "Describe lo que no quieres ver en el video", "videoGen.negativePromptPlaceholder": "Ingrese lo que desea excluir del video...", "videoGen.negativePromptTooltip": "Los indicadores negativos ayudan a excluir elementos, estilos o conceptos no deseados de la generación de video. Por ejemplo: \"borroso, baja calidad, caras distorsionadas\".", "videoGen.negativePromptSuggestions": "Suger<PERSON><PERSON><PERSON>", "videoGen.negativePromptSuggestion1": "borroso, baja calidad", "videoGen.negativePromptSuggestion2": "caras distorsionadas", "videoGen.negativePromptSuggestion3": "anatomía <PERSON>", "videoGen.negativePromptSuggestion4": "marca de agua, texto", "videoGen.negativePromptSuggestion5": "saturado", "videoGen.negativePromptSuggestion6": "movimiento poco realista", "videoGen.enhancePrompt": "Mejorar indicación", "videoGen.enhancePromptOn": "Los avisos siempre se refinarán para mejorar la calidad del resultado.", "videoGen.enhancePromptOnRequired": "Los mensajes siempre se refinarán para mejorar la calidad de salida (requerido para este modelo)", "videoGen.enhancePromptOff": "Los avisos no serán modificados.", "videoGen.enhancePromptLocked": "Esta configuración está bloqueada para el modelo seleccionado.", "videoGen.enhancePromptNotRefined": "Los indicativos no serán refinados", "videoGen.aspectRatio": "Relación de aspecto", "videoGen.duration": "Duración", "videoGen.selectDuration": "Seleccionar duración del video en segundos", "videoGen.creditsRemaining": "Créditos: {credits} restantes", "videoGen.generationCost": "Esta generación costará: {cost} Créditos por {duration}s", "videoGen.generateVideo": "Generar video", "videoGen.somethingWentWrong": "Algo salió mal.", "videoGen.examplesTitle": "Ejemplos de generación de video con IA", "videoGen.examplesDescription": "Explora el poder de la generación de video con IA con estas comparaciones interactivas", "videoGen.processing": "Procesando tu video...", "videoGen.analyzing": "Ana<PERSON><PERSON>do tu entrada...", "videoGen.rendering": "Procesando tu video...", "videoGen.finalizing": "Finalizando tu video...", "videoGen.almostReady": "Tu video está casi listo...", "videoGen.estimatedTime": "Tiempo estimado: 2-3 minutos", "videoGen.backgroundProcessing": "Tu video se está generando en segundo plano.", "videoGen.checkHistory": "<PERSON>uedes verificar el progreso en tu historial.", "videoGen.willNotify": "Te notificaremos cuando esté listo.", "videoGen.error.invalidPrompt": "Por favor ingrese un mensaje válido", "videoGen.error.invalidModel": "Por favor, selecciona un modelo válido.", "videoGen.error.invalidDuration": "Por favor, seleccione una duración válida.", "videoGen.error.invalidAspectRatio": "Por favor, selecciona una relación de aspecto válida.", "videoGen.error.insufficientCredits": "Créditos insuficientes para la generación de video", "videoGen.error.fileTooLarge": "El archivo de imagen seleccionado es demasiado grande.", "videoGen.error.invalidFileFormat": "Formato de archivo de imagen no válido", "videoGen.error.generationFailed": "La generación de video falló", "videoGen.error.networkError": "Error de red durante la generación de video", "videoGen.error.serverError": "Error del servidor durante la generación de video", "videoGen.success.generationStarted": "La generación de video comenzó con éxito.", "videoGen.success.generationCompleted": "La generación de video se completó con éxito.", "videoGen.success.imageUploaded": "Imagen subida con éxito", "videoGen.success.promptSaved": "Solicitud guardada con éxito", "Image Generation": "Generación de Imágenes", "We are starting to generate your image, please wait about 1 minute...": "Estamos comenzando a generar tu imagen, por favor espera aproximadamente 1 minuto...", "We are starting to generate speech from your document, please check back later...": "Estamos comenzando a generar el discurso a partir de su documento, por favor regrese más tarde...", "We are starting to generate your speech, please wait about {time} {unit}...": "Estamos empezando a generar su discurso, por favor espere aproximadamente {time} {unit}...", "minutes": "minutos", "seconds": "segundos", "faq.services.title": "<PERSON><PERSON><PERSON>", "faq.questions.q1.title": "¿Por qué elegir geminigen.ai sobre otras herramientas de TTV?", "faq.questions.q1.content": "Geminigen.ai ofrece imágenes y videos generados por IA a partir de texto a un precio más asequible en comparación con otras aplicaciones en el mercado. Además de la generación de imágenes y videos a partir de texto, también ofrecemos servicios de conversión de texto a voz y generación de conversaciones basadas en texto.", "faq.questions.q2.title": "¿Cómo utilizo el servicio de geminigen.ai?", "faq.questions.q2.content": "Nuestro servicio está diseñado para ser fácil de usar. Simplemente describe el video que deseas en texto, y el sistema lo convertirá automáticamente en un video.", "faq.questions.q3.title": "¿Necesito conocimientos de programación para usar geminigen.ai?", "faq.questions.q3.content": "No, no necesitas ningún conocimiento de programación. Hemos integrado la API TTV de Gemini en nuestro sitio web, haciendo que el proceso de convertir texto en imágenes y videos sea simple y conveniente para todos.", "faq.questions.q4.title": "¿Qué idiomas son compatibles para la entrada de texto?", "faq.questions.q4.content": "Actualmente, ofrecemos soporte para varios idiomas, incluyendo inglés, vietnamita, español y más. El soporte para idiomas adicionales se está agregando regularmente.", "faq.questions.q5.title": "¿Cuál es el costo de usar geminigen.ai?", "faq.questions.q5.content": "Ponemos precios basados en la tarifa de Gemini, garantizando que los costos sean más bajos que muchos otros herramientas de conversión de texto a voz en el mercado. Puede consultar los precios detallados en la página: https://geminigen.ai/pricing", "faq.questions.q6.title": "¿Puedo usar geminigen.ai con fines comerciales?", "faq.questions.q6.content": "S<PERSON>, nuestro servicio admite tanto fines personales como comerciales. Sin embargo, por favor, asegúrese de cumplir con nuestros términos de uso.", "faq.questions.q7.title": "¿Cuál es la calidad del video en geminigen.ai?", "faq.questions.q7.content": "Las imágenes y videos generados son de alta calidad, con visuales realistas y vívidos gracias a la avanzada tecnología TTV de Gemini.", "faq.questions.q8.title": "¿Cómo protejo mi privacidad y datos en geminigen.ai?", "faq.questions.q8.content": "La seguridad y la privacidad del usuario son nuestras principales prioridades. Empleamos medidas de seguridad avanzadas para proteger sus datos y no compartimos información con terceros sin su consentimiento.", "faq.questions.q9.title": "Para la conversión de texto a voz, ¿puedo editar o personalizar el audio de salida?", "faq.questions.q9.content": "Si bien no ofrecemos funciones de edición directa en la plataforma, puedes personalizar algunas configuraciones como la velocidad de lectura y el tono antes de la conversión. Esto te permite tener un mejor control sobre la sensación y el sonido final del archivo de salida.", "faq.questions.q10.title": "Para la conversión de texto a voz, ¿puedo solicitar voces adicionales o nuevos idiomas?", "faq.questions.q10.content": "Siempre escuchamos los comentarios de los usuarios y nos esforzamos por expandir nuestros servicios. Si tienes solicitudes específicas para una voz o idioma en particular, no dudes en enviarlas a nuestro sistema de soporte.", "faq.questions.q11.title": "¿Dónde puedo contactar con el soporte técnico si tengo problemas al usar el servicio?", "faq.questions.q11.content": "Brindamos soporte por correo electrónico y chat en vivo en el sitio web. Nuestro equipo de soporte está siempre listo para responder cualquier pregunta y asistirte cuando lo necesites.", "faq.questions.q12.title": "¿Necesito crear una cuenta para usar el servicio?", "faq.questions.q12.content": "<PERSON><PERSON>, crear una cuenta te ayuda a gestionar imágenes, videos y documentos convertidos fácilmente y acceder a funciones avanzadas y mejores servicios de atención al cliente.", "faq.questions.q13.title": "¿Puedo usar geminigen.ai para crear contenido para mi sitio web o blog?", "faq.questions.q13.content": "Sí, puede utilizar nuestro servicio para crear contenido de audio para su sitio web, blog o plataformas de redes sociales, enriqueciendo la forma en que entrega información a sus lectores o clientes.", "faq.questions.q14.title": "¿Cuál es el formato de archivo de salida de la imagen?", "faq.questions.q14.content": "El formato de archivo de salida principal es png, lo que garantiza la compatibilidad con la mayoría de los dispositivos.", "faq.questions.q15.title": "¿Cuánto cuesta generar un video?", "faq.questions.q15.content": "Calculamos los créditos basados en el número de segundos del video que creas. Puede consultar los precios detallados en la página: https://geminigen.ai/pricing", "faq.questions.q16.title": "¿Cuál es el formato de archivo de salida del discurso?", "faq.questions.q16.content": "El formato de archivo de salida principal es MP3, WAV, lo que garantiza la compatibilidad con la mayoría de los dispositivos y software de reproducción de música.", "faq.questions.q17.title": "¿Es complicado el proceso de pago? ¿Qué métodos de pago están disponibles?", "faq.questions.q17.content": "El proceso de pago es muy sencillo. Ofrecemos métodos de pago populares, incluidos PayPal, débito y criptomonedas.", "faq.questions.q18.title": "¿Existen planes de suscripción para usuarios frecuentes?", "faq.questions.q18.content": "No, vendemos créditos sin fecha de vencimiento. Cuando te quedes sin créditos, compra otro paquete de créditos.", "faq.questions.q19.title": "¿Recibiré un reembolso si mi video falla o tiene un error?", "faq.questions.q19.content": "Contaremos los créditos cuando tu video se haya creado con éxito. En caso de errores, los créditos no se contarán.", "Transform your ideas into stunning AI-generated images, videos, speech, and more.": "Transforma tus ideas en impresionantes imágenes, videos, discursos generados por IA y más.", "Save up to {0} compared to traditional creative services while experiencing the future of content generation.": "Ahorra hasta {0} en comparación con los servicios creativos tradicionales mientras experimentas el futuro de la generación de contenido.", "30 seconds": "30 segundos", "5 minutes": "5 minutos", "Minimum required": "<PERSON><PERSON><PERSON>", "Audio duration must be at least {duration}": "La duración del audio debe ser al menos {duration}", "The sound should be clear, without any noise, and last at least 5 minutes to ensure good quality.": "El sonido debe ser claro, sin ningún ruido, y durar al menos 5 minutos para garantizar una buena calidad.", "The sound should be clear, without any noise, and last at least 30 seconds to ensure good quality.": "El sonido debe ser claro, sin ningún ruido, y durar al menos 30 segundos para garantizar una buena calidad.", "videoGen.resolution": "Resolución", "videoGen.selectResolution": "Seleccionar calidad de resolución de video", "GEMINI_RATE_LIMIT": "El servidor actualmente está experimentando un alto tráfico. Por favor, inténtelo de nuevo más tarde.", "Create stunning images from text prompts using advanced AI models": "Crea imágenes impresionantes a partir de indicaciones de texto utilizando modelos avanzados de IA.", "AI Video Generator": "Generador de Videos por IA", "Create professional videos from text prompts using advanced AI models": "Crea videos profesionales a partir de indicaciones de texto utilizando modelos de IA avanzados.", "AI Speech Generator": "Generador de Voz de IA", "Convert text to natural-sounding speech with 400+ AI voices": "Convierte texto en discurso con sonido natural con más de 400 voces de IA.", "AI Dialogue Generator": "Generador de Diálogos de IA", "Create realistic conversations between multiple AI speakers": "Crea conversaciones realistas entre múltiples interlocutores de IA.", "Video Generation Pricing by Resolution": "Precios de Generación de Video por Resolución", "Different video resolutions have different pricing. Higher resolutions cost more but provide better quality.": "Diferentes resoluciones de video tienen precios diferentes. Las resoluciones más altas cuestan más, pero ofrecen mejor calidad.", "Base Price": "Precio base", "HD Resolution": "Resolución HD", "Full HD Resolution": "Resolución Full HD", "HD Resolution Only": "Solo resolución HD", "Full HD not available for Veo 2": "Full HD no disponible para Veo 2", "off": "<PERSON><PERSON><PERSON>", "Veo 2 HD": "Veo 2 HD", "Veo 3 Fast HD (With Audio)": "Veo 3 Fast HD (Con Audio)", "Veo 3 Fast Full HD (With Audio)": "Veo 3 Fast Full HD (Con Audio)", "Veo 3 HD": "Veo 3 HD", "Veo 3 Full HD": "Veo 3 Full HD", "Server": "<PERSON><PERSON><PERSON>", "Regular Server": "Servidor Regular", "VIP Server": "Servidor VIP", "Reliable but may be slower at peak times": "<PERSON>able, pero puede ser más lento en horas pico.", "Smooth, priority performance, no delays": "<PERSON>ave, rendimiento prioritario, sin retrasos", "Promotional Price": "Precio promocional", "NOT_ENOUGH_AND_LOCK_CREDIT": "Su cuenta no tiene suficiente crédito. Por favor recargue su cuenta para continuar.", "videoGen.creditsNeeded": "Créditos necesarios", "videoGen.creditsAvailable": "Créditos disponibles", "videoGen.creditsShortfall": "Déficit de créditos", "NOT_ENOUGH_CREDIT_TITLE": "Créditos insuficientes", "NOT_ENOUGH_CREDIT_MESSAGE": "Necesitas más créditos para generar este video. Recarga tu cuenta para seguir creando contenido increíble.", "BUY_CREDITS_NOW": "Compra Créditos Ahora", "VIEW_PRICING": "<PERSON><PERSON> precios", "videoGen.willPurchase": "Har<PERSON> una compra (mín. $10)", "NOT_ENOUGH_CREDIT_MESSAGE_VIDEO": "Necesitas más créditos para generar este video. Recarga tu cuenta para seguir creando contenido increíble.", "NOT_ENOUGH_CREDIT_MESSAGE_SPEECH": "Necesitas más créditos para generar este discurso. Recarga tu cuenta para continuar creando contenido increíble.", "NOT_ENOUGH_CREDIT_MESSAGE_DIALOG": "Necesitas más créditos para generar este diálogo. Recarga tu cuenta para seguir creando contenido increíble.", "NOT_ENOUGH_CREDIT_MESSAGE_IMAGE": "Necesitas más créditos para generar esta imagen. Recarga tu cuenta para seguir creando contenido increíble.", "creditsNeeded": "Créditos necesarios", "creditsAvailable": "Créditos disponibles", "creditsShortfall": "Déficit de créditos", "willPurchase": "Comprará", "MINIMUM_PURCHASE_10": "Compra mínima: $10", "imageGen.creditsNeeded": "Créditos necesarios", "imageGen.creditsAvailable": "Créditos disponibles", "imageGen.creditsShortfall": "Déficit de créditos", "imageGen.willPurchase": "Comprará", "NOT_ENOUGH_CREDIT_MESSAGE_VIDEO_C": "Necesitas más créditos para generar este video. Solo ${money}/{credit} créditos, {veo3FastCredit} créditos ~ ${veo3FastPrice}/video Veo 3 Fast, {veo3Credit} créditos ~ ${veo3Price}/video Veo 3, {veo2Credit} créditos ~ ${veo2Price}/video Veo 2. Ahorra hasta {save}%+ en comparación con Replicate y Fal.ai.", "NOT_ENOUGH_CREDIT_MESSAGE_IMAGE_C": "Necesitas más créditos para generar esta imagen. Solo ${money}/{credit} créditos, {imagenFlashCredit} créditos ~ ${imagenFlashPrice}/imagen Gemini 2.5 Flash, {imagen4FastCredit} créditos ~ ${imagen4FastPrice}/imagen Imagen 4 Fast, {imagen4Credit} créditos ~ ${imagen4Price}/imagen Imagen 4, {imagen4UltraCredit} créditos ~ ${imagen4UltraPrice}/imagen Imagen 4 Ultra. Ahorra hasta {save}%+ en comparación con Replicate & Fal.ai.", "NOT_ENOUGH_CREDIT_MESSAGE_SPEECH_C": "Necesitas más créditos para generar este discurso. Solo ${money}/{credit} créditos ~ {characters} caracteres ~ {hours} horas de audio creado. Ahorra más del {save}% en comparación con Elevenlabs.", "You can switch server type to see the price difference.": "<PERSON>uedes cambiar el tipo de servidor para ver la diferencia de precio.", "Unstable": "Inestable", "Stable": "Estable", "Special Promotion - 100% Bonus Credits!": "Promoción Especial - ¡Créditos de Bonificación del 100%!", "Get double credits with every purchase. Limited time offer!": "Obtén el doble de créditos con cada compra. ¡Oferta por tiempo limitado!", "100% Bonus Credits!": "¡100% de Créditos de Bonificación!", "100% Bonus Credits Active!": "¡Créditos de bonificación del 100% activos!", "Base: {base} + Bonus: {bonus}": "Base: {base} + Bonificación: {bonus}", "You'll receive: {total} credits ({base} base + {bonus} bonus)": "Recibirás: {total} créditos ({base} base + {bonus} bonificación)", "🎉 100% Bonus Credits Active! Get double credits with every purchase!": "🎉 ¡100% de créditos extra activos! ¡Obtén el doble de créditos con cada compra!", "The prompt must be in English. Please try again with a different prompt.": "El mensaje debe estar en inglés. Por favor, intenta de nuevo con un mensaje diferente."}